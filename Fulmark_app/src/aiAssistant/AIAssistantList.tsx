import { useState } from 'react';
import { List, useListContext } from 'react-admin';
import {
    Card,
    CardContent,
    Typography,
    Box,
    Grid,
    TextField,
    Button,
    Paper,
    Chip,
    Avatar,
    Divider,
    IconButton,
    CircularProgress,
} from '@mui/material';
import {
    SmartToy as AIIcon,
    Send as SendIcon,
    Psychology as BrainIcon,
    Analytics as AnalyticsIcon,
    Email as EmailIcon,
    Build as ServiceIcon,
    TrendingUp as TrendingUpIcon,
    Lightbulb as InsightIcon,
    AutoAwesome as MagicIcon,
    Chat as ChatIcon,
} from '@mui/icons-material';

const AIFeatureCard = ({ 
    title, 
    description, 
    icon, 
    color, 
    onClick, 
    status = 'active' 
}: {
    title: string;
    description: string;
    icon: React.ReactNode;
    color: string;
    onClick?: () => void;
    status?: 'active' | 'coming_soon' | 'beta';
}) => (
    <Card 
        sx={{ 
            cursor: onClick ? 'pointer' : 'default',
            '&:hover': onClick ? { transform: 'translateY(-2px)', boxShadow: 3 } : {},
            transition: 'all 0.2s ease-in-out',
        }}
        onClick={onClick}
    >
        <CardContent>
            <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                <Avatar sx={{ bgcolor: color, mr: 2 }}>
                    {icon}
                </Avatar>
                <Box sx={{ flexGrow: 1 }}>
                    <Typography variant="h6">{title}</Typography>
                    <Chip 
                        label={status === 'active' ? 'Aktywne' : status === 'beta' ? 'Beta' : 'Wkrótce'}
                        size="small"
                        color={status === 'active' ? 'success' : status === 'beta' ? 'warning' : 'default'}
                    />
                </Box>
            </Box>
            <Typography variant="body2" color="textSecondary">
                {description}
            </Typography>
        </CardContent>
    </Card>
);

const ChatInterface = () => {
    const [message, setMessage] = useState('');
    const [chatHistory, setChatHistory] = useState([
        {
            type: 'ai',
            message: 'Witaj! Jestem AI Asystentem HVAC. Mogę pomóc Ci w analizie danych, generowaniu raportów, optymalizacji procesów i wiele więcej. Jak mogę Ci dzisiaj pomóc?',
            timestamp: new Date(),
        }
    ]);
    const [isLoading, setIsLoading] = useState(false);

    const handleSendMessage = async () => {
        if (!message.trim()) return;

        const userMessage = {
            type: 'user',
            message: message,
            timestamp: new Date(),
        };

        setChatHistory(prev => [...prev, userMessage]);
        setMessage('');
        setIsLoading(true);

        // Simulate AI response
        setTimeout(() => {
            const aiResponse = {
                type: 'ai',
                message: generateAIResponse(message),
                timestamp: new Date(),
            };
            setChatHistory(prev => [...prev, aiResponse]);
            setIsLoading(false);
        }, 1500);
    };

    const generateAIResponse = (userMessage: string) => {
        const responses = [
            'Analizuję Twoje zapytanie... Na podstawie danych z systemu CRM, mogę zasugerować następujące działania.',
            'Sprawdziłem dane HVAC i znalazłem kilka interesujących wzorców. Oto moja analiza:',
            'Na podstawie historii serwisowej i danych klientów, oto moje rekomendacje:',
            'Przeanalizowałem wydajność techników i harmonogram serwisu. Oto optymalizacje:',
        ];
        return responses[Math.floor(Math.random() * responses.length)];
    };

    return (
        <Card sx={{ height: 600 }}>
            <CardContent sx={{ height: '100%', display: 'flex', flexDirection: 'column' }}>
                <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                    <ChatIcon sx={{ mr: 1, color: 'primary.main' }} />
                    <Typography variant="h6">Chat z AI Asystentem</Typography>
                </Box>
                
                <Box sx={{ flexGrow: 1, overflow: 'auto', mb: 2 }}>
                    {chatHistory.map((chat, index) => (
                        <Box key={index} sx={{ mb: 2 }}>
                            <Box sx={{ 
                                display: 'flex', 
                                justifyContent: chat.type === 'user' ? 'flex-end' : 'flex-start',
                                mb: 1 
                            }}>
                                <Paper 
                                    sx={{ 
                                        p: 2, 
                                        maxWidth: '70%',
                                        bgcolor: chat.type === 'user' ? 'primary.main' : 'grey.100',
                                        color: chat.type === 'user' ? 'white' : 'text.primary',
                                    }}
                                >
                                    <Typography variant="body2">{chat.message}</Typography>
                                </Paper>
                            </Box>
                        </Box>
                    ))}
                    {isLoading && (
                        <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                            <CircularProgress size={20} sx={{ mr: 1 }} />
                            <Typography variant="body2" color="textSecondary">
                                AI pisze odpowiedź...
                            </Typography>
                        </Box>
                    )}
                </Box>

                <Box sx={{ display: 'flex', gap: 1 }}>
                    <TextField
                        fullWidth
                        variant="outlined"
                        placeholder="Napisz wiadomość do AI..."
                        value={message}
                        onChange={(e) => setMessage(e.target.value)}
                        onKeyPress={(e) => e.key === 'Enter' && handleSendMessage()}
                        size="small"
                    />
                    <IconButton 
                        color="primary" 
                        onClick={handleSendMessage}
                        disabled={!message.trim() || isLoading}
                    >
                        <SendIcon />
                    </IconButton>
                </Box>
            </CardContent>
        </Card>
    );
};

const AIInsights = () => {
    const insights = [
        {
            title: 'Optymalizacja Tras',
            description: 'AI wykrył możliwość oszczędzenia 25% czasu podróży przez lepsze planowanie tras techników.',
            type: 'optimization',
            impact: 'high',
        },
        {
            title: 'Predykcja Awarii',
            description: 'System przewiduje potencjalne awarie u 3 klientów w następnym tygodniu.',
            type: 'prediction',
            impact: 'medium',
        },
        {
            title: 'Analiza Rentowności',
            description: 'Serwis klimatyzacji przynosi 40% wyższą marżę niż instalacje.',
            type: 'analysis',
            impact: 'high',
        },
    ];

    return (
        <Card>
            <CardContent>
                <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                    <InsightIcon sx={{ mr: 1, color: 'primary.main' }} />
                    <Typography variant="h6">AI Insights</Typography>
                </Box>
                {insights.map((insight, index) => (
                    <Box key={index}>
                        <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', mb: 1 }}>
                            <Typography variant="subtitle1">{insight.title}</Typography>
                            <Chip 
                                label={insight.impact === 'high' ? 'Wysoki wpływ' : 'Średni wpływ'}
                                color={insight.impact === 'high' ? 'error' : 'warning'}
                                size="small"
                            />
                        </Box>
                        <Typography variant="body2" color="textSecondary" sx={{ mb: 2 }}>
                            {insight.description}
                        </Typography>
                        {index < insights.length - 1 && <Divider sx={{ mb: 2 }} />}
                    </Box>
                ))}
            </CardContent>
        </Card>
    );
};

export const AIAssistantList = () => {
    const [selectedFeature, setSelectedFeature] = useState<string | null>(null);

    const aiFeatures = [
        {
            id: 'email_analysis',
            title: 'Analiza Email',
            description: 'Automatyczna analiza i kategoryzacja emaili od klientów z wykorzystaniem AI.',
            icon: <EmailIcon />,
            color: '#2196f3',
            status: 'active' as const,
        },
        {
            id: 'predictive_maintenance',
            title: 'Predykcyjna Konserwacja',
            description: 'Przewidywanie awarii sprzętu HVAC na podstawie danych historycznych.',
            icon: <ServiceIcon />,
            color: '#4caf50',
            status: 'beta' as const,
        },
        {
            id: 'performance_analytics',
            title: 'Analityka Wydajności',
            description: 'Analiza wydajności techników i optymalizacja procesów serwisowych.',
            icon: <AnalyticsIcon />,
            color: '#ff9800',
            status: 'active' as const,
        },
        {
            id: 'customer_insights',
            title: 'Insights Klientów',
            description: 'Głęboka analiza zachowań klientów i przewidywanie ich potrzeb.',
            icon: <BrainIcon />,
            color: '#9c27b0',
            status: 'active' as const,
        },
        {
            id: 'revenue_optimization',
            title: 'Optymalizacja Przychodów',
            description: 'AI-powered rekomendacje dla zwiększenia rentowności usług.',
            icon: <TrendingUpIcon />,
            color: '#f44336',
            status: 'beta' as const,
        },
        {
            id: 'smart_scheduling',
            title: 'Inteligentne Planowanie',
            description: 'Automatyczne planowanie wizyt techników z optymalizacją tras.',
            icon: <MagicIcon />,
            color: '#607d8b',
            status: 'coming_soon' as const,
        },
    ];

    return (
        <List title="AI Asystent HVAC">
            <Box sx={{ p: 2 }}>
                <Grid container spacing={3}>
                    <Grid item xs={12}>
                        <Card sx={{ mb: 3, background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)' }}>
                            <CardContent>
                                <Box sx={{ display: 'flex', alignItems: 'center', color: 'white' }}>
                                    <AIIcon sx={{ fontSize: 48, mr: 2 }} />
                                    <Box>
                                        <Typography variant="h4" sx={{ color: 'white', mb: 1 }}>
                                            AI Asystent HVAC
                                        </Typography>
                                        <Typography variant="h6" sx={{ color: 'rgba(255,255,255,0.8)' }}>
                                            Inteligentne rozwiązania dla Twojego biznesu HVAC
                                        </Typography>
                                    </Box>
                                </Box>
                            </CardContent>
                        </Card>
                    </Grid>

                    <Grid item xs={12} md={8}>
                        <Grid container spacing={2}>
                            {aiFeatures.map((feature) => (
                                <Grid item xs={12} md={6} key={feature.id}>
                                    <AIFeatureCard
                                        title={feature.title}
                                        description={feature.description}
                                        icon={feature.icon}
                                        color={feature.color}
                                        status={feature.status}
                                        onClick={() => setSelectedFeature(feature.id)}
                                    />
                                </Grid>
                            ))}
                        </Grid>
                    </Grid>

                    <Grid item xs={12} md={4}>
                        <AIInsights />
                    </Grid>

                    <Grid item xs={12}>
                        <ChatInterface />
                    </Grid>
                </Grid>
            </Box>
        </List>
    );
};
