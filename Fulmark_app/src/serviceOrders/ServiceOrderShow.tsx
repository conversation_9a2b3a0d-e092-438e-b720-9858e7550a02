import {
    Show,
    SimpleShowLayout,
    TextField,
    DateField,
    ReferenceField,
    SelectField,
    NumberField,
    FunctionField,
    TopToolbar,
    EditButton,
    DeleteButton,
    ListButton,
} from 'react-admin';
import { Grid, Typography, Card, CardContent, Chip, Box } from '@mui/material';

const ServiceOrderShowActions = () => (
    <TopToolbar>
        <ListButton />
        <EditButton />
        <DeleteButton />
    </TopToolbar>
);

const StatusChip = ({ record }: { record?: any }) => {
    if (!record) return null;
    
    const statusColors: Record<string, string> = {
        new: '#2196f3',
        scheduled: '#ff9800',
        in_progress: '#4caf50',
        completed: '#8bc34a',
        cancelled: '#f44336',
    };

    return (
        <Chip
            label={record.status}
            style={{
                backgroundColor: statusColors[record.status] || '#757575',
                color: 'white',
                textTransform: 'capitalize',
            }}
        />
    );
};

const PriorityChip = ({ record }: { record?: any }) => {
    if (!record) return null;
    
    const priorityColors: Record<string, string> = {
        low: '#4caf50',
        medium: '#ff9800',
        high: '#f44336',
        urgent: '#9c27b0',
    };

    return (
        <Chip
            label={record.priority}
            style={{
                backgroundColor: priorityColors[record.priority] || '#757575',
                color: 'white',
                textTransform: 'capitalize',
            }}
        />
    );
};

export const ServiceOrderShow = () => (
    <Show actions={<ServiceOrderShowActions />}>
        <SimpleShowLayout>
            <Grid container spacing={3}>
                {/* Basic Information */}
                <Grid item xs={12}>
                    <Card>
                        <CardContent>
                            <Typography variant="h6" gutterBottom>
                                Service Order Details
                            </Typography>
                            <Grid container spacing={2}>
                                <Grid item xs={12} md={6}>
                                    <TextField source="title" />
                                </Grid>
                                <Grid item xs={12} md={3}>
                                    <FunctionField source="status" render={StatusChip} />
                                </Grid>
                                <Grid item xs={12} md={3}>
                                    <FunctionField source="priority" render={PriorityChip} />
                                </Grid>
                                <Grid item xs={12} md={6}>
                                    <SelectField
                                        source="service_type"
                                        choices={[
                                            { id: 'installation', name: 'Installation' },
                                            { id: 'maintenance', name: 'Maintenance' },
                                            { id: 'repair', name: 'Repair' },
                                            { id: 'inspection', name: 'Inspection' },
                                        ]}
                                    />
                                </Grid>
                                <Grid item xs={12}>
                                    <TextField source="description" />
                                </Grid>
                            </Grid>
                        </CardContent>
                    </Card>
                </Grid>

                {/* Customer Information */}
                <Grid item xs={12} md={6}>
                    <Card>
                        <CardContent>
                            <Typography variant="h6" gutterBottom>
                                Customer Information
                            </Typography>
                            <ReferenceField source="company_id" reference="companies" link="show">
                                <TextField source="name" />
                            </ReferenceField>
                            <ReferenceField source="contact_id" reference="contacts" link="show">
                                <FunctionField
                                    render={(record: any) => `${record.first_name} ${record.last_name}`}
                                />
                            </ReferenceField>
                            <ReferenceField source="equipment_id" reference="equipment" link="show">
                                <FunctionField
                                    render={(record: any) => 
                                        record ? `${record.brand} ${record.model}` : 'No equipment assigned'
                                    }
                                />
                            </ReferenceField>
                        </CardContent>
                    </Card>
                </Grid>

                {/* Scheduling Information */}
                <Grid item xs={12} md={6}>
                    <Card>
                        <CardContent>
                            <Typography variant="h6" gutterBottom>
                                Scheduling
                            </Typography>
                            <DateField source="scheduled_date" showTime />
                            <DateField source="completed_date" showTime />
                            <ReferenceField source="technician_id" reference="technicians" link="show">
                                <FunctionField
                                    render={(record: any) => 
                                        record ? `${record.first_name} ${record.last_name}` : 'Unassigned'
                                    }
                                />
                            </ReferenceField>
                            <NumberField source="estimated_duration" label="Estimated Duration (min)" />
                            <NumberField source="actual_duration" label="Actual Duration (min)" />
                        </CardContent>
                    </Card>
                </Grid>

                {/* Work Details */}
                <Grid item xs={12}>
                    <Card>
                        <CardContent>
                            <Typography variant="h6" gutterBottom>
                                Work Details
                            </Typography>
                            <Grid container spacing={2}>
                                <Grid item xs={12} md={6}>
                                    <Box>
                                        <Typography variant="subtitle2" gutterBottom>
                                            Work Performed
                                        </Typography>
                                        <TextField source="work_performed" />
                                    </Box>
                                </Grid>
                                <Grid item xs={12} md={6}>
                                    <Box>
                                        <Typography variant="subtitle2" gutterBottom>
                                            Recommendations
                                        </Typography>
                                        <TextField source="recommendations" />
                                    </Box>
                                </Grid>
                            </Grid>
                        </CardContent>
                    </Card>
                </Grid>

                {/* Cost Information */}
                <Grid item xs={12}>
                    <Card>
                        <CardContent>
                            <Typography variant="h6" gutterBottom>
                                Cost Breakdown
                            </Typography>
                            <Grid container spacing={2}>
                                <Grid item xs={12} md={3}>
                                    <NumberField 
                                        source="parts_cost" 
                                        options={{ style: 'currency', currency: 'PLN' }}
                                        label="Parts Cost"
                                    />
                                </Grid>
                                <Grid item xs={12} md={3}>
                                    <NumberField 
                                        source="labor_cost" 
                                        options={{ style: 'currency', currency: 'PLN' }}
                                        label="Labor Cost"
                                    />
                                </Grid>
                                <Grid item xs={12} md={3}>
                                    <NumberField 
                                        source="total_cost" 
                                        options={{ style: 'currency', currency: 'PLN' }}
                                        label="Total Cost"
                                    />
                                </Grid>
                                <Grid item xs={12} md={3}>
                                    <ReferenceField source="invoice_id" reference="invoices" link="show">
                                        <TextField source="invoice_number" />
                                    </ReferenceField>
                                </Grid>
                            </Grid>
                        </CardContent>
                    </Card>
                </Grid>

                {/* Timestamps */}
                <Grid item xs={12} md={6}>
                    <Card>
                        <CardContent>
                            <Typography variant="h6" gutterBottom>
                                Timeline
                            </Typography>
                            <DateField source="created_at" showTime label="Created" />
                            <DateField source="updated_at" showTime label="Last Updated" />
                        </CardContent>
                    </Card>
                </Grid>
            </Grid>
        </SimpleShowLayout>
    </Show>
);
