import {
    List,
    Datagrid,
    TextField,
    DateField,
    ReferenceField,
    SelectField,
    NumberField,
    FunctionField,
    EditButton,
    ShowButton,
    DeleteButton,
    TopToolbar,
    CreateButton,
    ExportButton,
    FilterButton,
    SearchInput,
    SelectInput,
    DateInput,
    ReferenceInput,
    AutocompleteInput,
} from 'react-admin';
import { Chip, Box, Typography, Tabs, Tab } from '@mui/material';
import { useState } from 'react';
import { ServiceOrdersKanban } from './ServiceOrdersKanban';
import { ServiceOrderStatus, PriorityIndicator } from '../components/cosmic/CosmicStatusIndicator';
import { CosmicButton } from '../components/cosmic/CosmicButton';

const serviceOrderFilters = [
    <SearchInput source="q" alwaysOn />,
    <SelectInput
        source="status"
        choices={[
            { id: 'new', name: 'New' },
            { id: 'scheduled', name: 'Scheduled' },
            { id: 'in_progress', name: 'In Progress' },
            { id: 'completed', name: 'Completed' },
            { id: 'cancelled', name: 'Cancelled' },
        ]}
    />,
    <SelectInput
        source="priority"
        choices={[
            { id: 'low', name: 'Low' },
            { id: 'medium', name: 'Medium' },
            { id: 'high', name: 'High' },
            { id: 'urgent', name: 'Urgent' },
        ]}
    />,
    <SelectInput
        source="service_type"
        choices={[
            { id: 'installation', name: 'Installation' },
            { id: 'maintenance', name: 'Maintenance' },
            { id: 'repair', name: 'Repair' },
            { id: 'inspection', name: 'Inspection' },
        ]}
    />,
    <ReferenceInput source="technician_id" reference="technicians">
        <AutocompleteInput
            optionText={(choice: any) => `${choice.first_name} ${choice.last_name}`}
        />
    </ReferenceInput>,
    <DateInput source="scheduled_date_gte" label="Scheduled After" />,
    <DateInput source="scheduled_date_lte" label="Scheduled Before" />,
];

const ServiceOrderListActions = () => (
    <TopToolbar>
        <FilterButton />
        <CosmicButton cosmic startIcon={<span>+</span>}>
            New Service Order
        </CosmicButton>
        <ExportButton />
    </TopToolbar>
);

const StatusField = ({ record }: { record?: any }) => {
    if (!record) return null;
    return <ServiceOrderStatus status={record.status} />;
};

const PriorityField = ({ record }: { record?: any }) => {
    if (!record) return null;
    return <PriorityIndicator priority={record.priority} />;
};

export const ServiceOrderList = () => {
    const [viewMode, setViewMode] = useState<'list' | 'kanban'>('list');

    const handleViewChange = (_: any, newValue: 'list' | 'kanban') => {
        setViewMode(newValue);
    };

    return (
        <Box>
            {/* View Mode Tabs */}
            <Box sx={{ borderBottom: 1, borderColor: 'divider', mb: 2 }}>
                <Tabs value={viewMode} onChange={handleViewChange}>
                    <Tab label="List View" value="list" />
                    <Tab label="Kanban Board" value="kanban" />
                </Tabs>
            </Box>

            {viewMode === 'kanban' ? (
                <ServiceOrdersKanban
                    onEdit={(id) => window.location.href = `#/service_orders/${id}`}
                    onView={(id) => window.location.href = `#/service_orders/${id}/show`}
                />
            ) : (
                <List
                    filters={serviceOrderFilters}
                    actions={<ServiceOrderListActions />}
                    sort={{ field: 'created_at', order: 'DESC' }}
                    perPage={25}
                >
                    <Datagrid rowClick="show" bulkActionButtons={false}>
                        <TextField source="title" />
                        <ReferenceField source="company_id" reference="companies" link="show">
                            <TextField source="name" />
                        </ReferenceField>
                        <ReferenceField source="contact_id" reference="contacts" link="show">
                            <FunctionField
                                render={(record: any) => `${record.first_name} ${record.last_name}`}
                            />
                        </ReferenceField>
                        <SelectField
                            source="service_type"
                            choices={[
                                { id: 'installation', name: 'Installation' },
                                { id: 'maintenance', name: 'Maintenance' },
                                { id: 'repair', name: 'Repair' },
                                { id: 'inspection', name: 'Inspection' },
                            ]}
                        />
                        <FunctionField source="status" render={StatusField} />
                        <FunctionField source="priority" render={PriorityField} />
                        <DateField source="scheduled_date" showTime />
                        <ReferenceField source="technician_id" reference="technicians" link={false}>
                            <FunctionField
                                render={(record: any) =>
                                    record ? `${record.first_name} ${record.last_name}` : 'Unassigned'
                                }
                            />
                        </ReferenceField>
                        <NumberField source="total_cost" options={{ style: 'currency', currency: 'PLN' }} />
                        <DateField source="created_at" showTime />
                        <ShowButton />
                        <EditButton />
                        <DeleteButton />
                    </Datagrid>
                </List>
            )}
        </Box>
    );
};
