import React, { useState, useMemo } from 'react';
import {
    Box,
    Grid,
    Typography,
    Avatar,
    IconButton,
    Tooltip,
    Chip,
    useTheme,
    alpha,
} from '@mui/material';
import {
    Edit as EditIcon,
    Visibility as ViewIcon,
    Schedule as ScheduleIcon,
    Person as PersonIcon,
    LocationOn as LocationIcon,
    AttachMoney as MoneyIcon,
} from '@mui/icons-material';
import { useGetList, useUpdate } from 'react-admin';
import { CosmicCard } from '../components/cosmic/CosmicCard';
import { ServiceOrderStatus, PriorityIndicator } from '../components/cosmic/CosmicStatusIndicator';
import { ServiceOrder } from '../types';

// Kanban Column Configuration
const kanbanColumns = [
    {
        id: 'new',
        title: 'New Orders',
        color: '#2196f3',
        icon: '📋',
        description: 'Newly created service orders',
    },
    {
        id: 'scheduled',
        title: 'Scheduled',
        color: '#ff9800',
        icon: '📅',
        description: 'Orders scheduled for execution',
    },
    {
        id: 'in_progress',
        title: 'In Progress',
        color: '#4caf50',
        icon: '🔧',
        description: 'Orders currently being executed',
    },
    {
        id: 'completed',
        title: 'Completed',
        color: '#8bc34a',
        icon: '✅',
        description: 'Successfully completed orders',
    },
];

// Service Order Card Component
const ServiceOrderCard: React.FC<{
    order: ServiceOrder;
    onEdit: (id: string) => void;
    onView: (id: string) => void;
}> = ({ order, onEdit, onView }) => {
    const theme = useTheme();
    
    const formatCurrency = (amount?: number) => {
        if (!amount) return 'N/A';
        return new Intl.NumberFormat('pl-PL', {
            style: 'currency',
            currency: 'PLN',
        }).format(amount);
    };
    
    const formatDate = (dateString?: string) => {
        if (!dateString) return 'Not scheduled';
        return new Date(dateString).toLocaleDateString('pl-PL', {
            day: '2-digit',
            month: '2-digit',
            hour: '2-digit',
            minute: '2-digit',
        });
    };
    
    const isUrgent = order.priority === 'urgent';
    const isOverdue = order.scheduled_date && new Date(order.scheduled_date) < new Date();

    return (
        <CosmicCard
            variant="elevated"
            interactive
            urgent={isUrgent}
            sx={{
                mb: 2,
                border: isOverdue ? `2px solid ${theme.palette.error.main}` : 'none',
                '&:hover': {
                    transform: 'translateY(-2px)',
                    boxShadow: '0 8px 25px rgba(0, 0, 0, 0.15)',
                },
            }}
            actions={
                <Box display="flex" gap={1}>
                    <Tooltip title="View Details">
                        <IconButton
                            size="small"
                            onClick={() => onView(order.id.toString())}
                            sx={{ color: theme.palette.primary.main }}
                        >
                            <ViewIcon fontSize="small" />
                        </IconButton>
                    </Tooltip>
                    <Tooltip title="Edit Order">
                        <IconButton
                            size="small"
                            onClick={() => onEdit(order.id.toString())}
                            sx={{ color: theme.palette.secondary.main }}
                        >
                            <EditIcon fontSize="small" />
                        </IconButton>
                    </Tooltip>
                </Box>
            }
        >
            {/* Header */}
            <Box display="flex" justifyContent="space-between" alignItems="flex-start" mb={2}>
                <Typography variant="h6" fontWeight={600} sx={{ flex: 1, mr: 1 }}>
                    {order.title}
                </Typography>
                <PriorityIndicator priority={order.priority as any} variant="chip" />
            </Box>
            
            {/* Service Type */}
            <Box mb={2}>
                <Chip
                    label={order.service_type.replace('_', ' ')}
                    size="small"
                    variant="outlined"
                    sx={{
                        textTransform: 'capitalize',
                        borderColor: theme.palette.primary.main,
                        color: theme.palette.primary.main,
                    }}
                />
            </Box>
            
            {/* Customer Info */}
            <Box display="flex" alignItems="center" mb={2}>
                <Avatar
                    sx={{
                        width: 32,
                        height: 32,
                        mr: 1,
                        bgcolor: theme.palette.primary.main,
                        fontSize: '0.875rem',
                    }}
                >
                    {order.company_name?.[0] || 'C'}
                </Avatar>
                <Box flex={1}>
                    <Typography variant="body2" fontWeight={500}>
                        {order.company_name || 'Unknown Company'}
                    </Typography>
                    <Typography variant="caption" color="text.secondary">
                        {order.contact_name || 'No contact'}
                    </Typography>
                </Box>
            </Box>
            
            {/* Scheduled Date */}
            {order.scheduled_date && (
                <Box display="flex" alignItems="center" mb={1}>
                    <ScheduleIcon
                        fontSize="small"
                        sx={{
                            mr: 1,
                            color: isOverdue ? theme.palette.error.main : theme.palette.text.secondary,
                        }}
                    />
                    <Typography
                        variant="body2"
                        color={isOverdue ? 'error' : 'text.secondary'}
                        fontWeight={isOverdue ? 600 : 400}
                    >
                        {formatDate(order.scheduled_date)}
                        {isOverdue && ' (Overdue)'}
                    </Typography>
                </Box>
            )}
            
            {/* Technician */}
            {order.technician_name && (
                <Box display="flex" alignItems="center" mb={1}>
                    <PersonIcon fontSize="small" sx={{ mr: 1, color: theme.palette.text.secondary }} />
                    <Typography variant="body2" color="text.secondary">
                        {order.technician_name}
                    </Typography>
                </Box>
            )}
            
            {/* Equipment */}
            {order.equipment_info && (
                <Box display="flex" alignItems="center" mb={1}>
                    <LocationIcon fontSize="small" sx={{ mr: 1, color: theme.palette.text.secondary }} />
                    <Typography variant="body2" color="text.secondary" noWrap>
                        {order.equipment_info}
                    </Typography>
                </Box>
            )}
            
            {/* Cost */}
            {order.total_cost && (
                <Box display="flex" alignItems="center" mt={2}>
                    <MoneyIcon fontSize="small" sx={{ mr: 1, color: theme.palette.success.main }} />
                    <Typography variant="body2" fontWeight={600} color="success.main">
                        {formatCurrency(order.total_cost)}
                    </Typography>
                </Box>
            )}
            
            {/* Description Preview */}
            {order.description && (
                <Typography
                    variant="body2"
                    color="text.secondary"
                    sx={{
                        mt: 1,
                        overflow: 'hidden',
                        textOverflow: 'ellipsis',
                        display: '-webkit-box',
                        WebkitLineClamp: 2,
                        WebkitBoxOrient: 'vertical',
                    }}
                >
                    {order.description}
                </Typography>
            )}
        </CosmicCard>
    );
};

// Kanban Column Component
const KanbanColumn: React.FC<{
    column: typeof kanbanColumns[0];
    orders: ServiceOrder[];
    onEdit: (id: string) => void;
    onView: (id: string) => void;
}> = ({ column, orders, onEdit, onView }) => {
    const theme = useTheme();
    
    return (
        <Box
            sx={{
                bgcolor: alpha(column.color, 0.05),
                borderRadius: 2,
                p: 2,
                minHeight: 600,
                border: `1px solid ${alpha(column.color, 0.1)}`,
            }}
        >
            {/* Column Header */}
            <Box
                display="flex"
                alignItems="center"
                justifyContent="space-between"
                mb={3}
                sx={{
                    pb: 2,
                    borderBottom: `2px solid ${alpha(column.color, 0.2)}`,
                }}
            >
                <Box display="flex" alignItems="center">
                    <Typography variant="h6" sx={{ mr: 1, fontSize: '1.5rem' }}>
                        {column.icon}
                    </Typography>
                    <Box>
                        <Typography variant="h6" fontWeight={600} color="text.primary">
                            {column.title}
                        </Typography>
                        <Typography variant="caption" color="text.secondary">
                            {column.description}
                        </Typography>
                    </Box>
                </Box>
                <Chip
                    label={orders.length}
                    size="small"
                    sx={{
                        bgcolor: column.color,
                        color: 'white',
                        fontWeight: 600,
                        minWidth: 32,
                    }}
                />
            </Box>
            
            {/* Orders */}
            <Box>
                {orders.length === 0 ? (
                    <Box
                        display="flex"
                        flexDirection="column"
                        alignItems="center"
                        justifyContent="center"
                        py={4}
                        sx={{ opacity: 0.5 }}
                    >
                        <Typography variant="body2" color="text.secondary" textAlign="center">
                            No {column.title.toLowerCase()}
                        </Typography>
                    </Box>
                ) : (
                    orders.map((order) => (
                        <ServiceOrderCard
                            key={order.id}
                            order={order}
                            onEdit={onEdit}
                            onView={onView}
                        />
                    ))
                )}
            </Box>
        </Box>
    );
};

// Main Kanban Component
export const ServiceOrdersKanban: React.FC<{
    onEdit: (id: string) => void;
    onView: (id: string) => void;
}> = ({ onEdit, onView }) => {
    const { data: serviceOrders, isLoading } = useGetList<ServiceOrder>('service_orders', {
        pagination: { page: 1, perPage: 1000 },
        sort: { field: 'created_at', order: 'DESC' },
    });
    
    // Group orders by status
    const groupedOrders = useMemo(() => {
        if (!serviceOrders) return {};
        
        return serviceOrders.reduce((acc, order) => {
            const status = order.status;
            if (!acc[status]) {
                acc[status] = [];
            }
            acc[status].push(order);
            return acc;
        }, {} as Record<string, ServiceOrder[]>);
    }, [serviceOrders]);
    
    if (isLoading) {
        return (
            <Box p={3}>
                <Typography>Loading service orders...</Typography>
            </Box>
        );
    }
    
    return (
        <Box p={3}>
            <Typography variant="h4" fontWeight={700} mb={3}>
                Service Orders Kanban Board
            </Typography>
            
            <Grid container spacing={3}>
                {kanbanColumns.map((column) => (
                    <Grid item xs={12} md={3} key={column.id}>
                        <KanbanColumn
                            column={column}
                            orders={groupedOrders[column.id] || []}
                            onEdit={onEdit}
                            onView={onView}
                        />
                    </Grid>
                ))}
            </Grid>
        </Box>
    );
};
