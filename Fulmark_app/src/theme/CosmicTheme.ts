import { createTheme, ThemeOptions } from '@mui/material/styles';
import { deepmerge } from '@mui/utils';

// Golden Ratio for perfect spacing
const PHI = 1.618;

// HVAC Professional Color Palette
const hvacColors = {
    primary: {
        main: '#1976d2',        // Professional Blue
        light: '#42a5f5',       // Light Blue
        dark: '#1565c0',        // Dark Blue
        contrastText: '#ffffff',
    },
    secondary: {
        main: '#ff6f00',        // HVAC Orange
        light: '#ff9800',       // Light Orange
        dark: '#e65100',        // Dark Orange
        contrastText: '#ffffff',
    },
    success: {
        main: '#4caf50',        // Success Green
        light: '#81c784',       // Light Green
        dark: '#388e3c',        // Dark Green
        contrastText: '#ffffff',
    },
    warning: {
        main: '#ff9800',        // Warning Orange
        light: '#ffb74d',       // Light Warning
        dark: '#f57c00',        // Dark Warning
        contrastText: '#000000',
    },
    error: {
        main: '#f44336',        // Error Red
        light: '#e57373',       // Light Red
        dark: '#d32f2f',        // Dark Red
        contrastText: '#ffffff',
    },
    info: {
        main: '#2196f3',        // Info Blue
        light: '#64b5f6',       // Light Info
        dark: '#1976d2',        // Dark Info
        contrastText: '#ffffff',
    },
    // HVAC-specific colors
    hvac: {
        cooling: '#2196f3',     // Cooling Blue
        heating: '#ff6f00',     // Heating Orange
        ventilation: '#4caf50', // Ventilation Green
        maintenance: '#9c27b0', // Maintenance Purple
        urgent: '#f44336',      // Urgent Red
        scheduled: '#ff9800',   // Scheduled Orange
        completed: '#4caf50',   // Completed Green
        inactive: '#757575',    // Inactive Gray
    },
};

// Golden Ratio Spacing System
const spacing = {
    xs: 4,                      // 4px
    sm: Math.round(4 * PHI),    // 6px
    md: Math.round(8 * PHI),    // 13px
    lg: Math.round(16 * PHI),   // 26px
    xl: Math.round(32 * PHI),   // 52px
    xxl: Math.round(64 * PHI),  // 104px
};

// Cosmic Animation System
const animations = {
    duration: {
        shortest: 150,
        shorter: 200,
        short: 250,
        standard: 300,
        complex: 375,
        enteringScreen: 225,
        leavingScreen: 195,
    },
    easing: {
        easeInOut: 'cubic-bezier(0.4, 0, 0.2, 1)',
        easeOut: 'cubic-bezier(0.0, 0, 0.2, 1)',
        easeIn: 'cubic-bezier(0.4, 0, 1, 1)',
        sharp: 'cubic-bezier(0.4, 0, 0.6, 1)',
        cosmic: 'cubic-bezier(0.25, 0.46, 0.45, 0.94)',
    },
};

// Cosmic Shadows
const shadows = {
    cosmic: '0 8px 32px rgba(0, 0, 0, 0.12)',
    hover: '0 12px 40px rgba(0, 0, 0, 0.15)',
    active: '0 4px 16px rgba(0, 0, 0, 0.1)',
    card: '0 2px 8px rgba(0, 0, 0, 0.08)',
    modal: '0 24px 64px rgba(0, 0, 0, 0.2)',
};

// Base Theme Configuration
const baseTheme: ThemeOptions = {
    palette: {
        mode: 'light',
        ...hvacColors,
        background: {
            default: '#fafafa',
            paper: '#ffffff',
        },
        text: {
            primary: 'rgba(0, 0, 0, 0.87)',
            secondary: 'rgba(0, 0, 0, 0.6)',
        },
    },
    typography: {
        fontFamily: '"Inter", "Roboto", "Helvetica", "Arial", sans-serif',
        h1: {
            fontSize: '2.5rem',
            fontWeight: 700,
            lineHeight: 1.2,
            letterSpacing: '-0.02em',
        },
        h2: {
            fontSize: '2rem',
            fontWeight: 600,
            lineHeight: 1.3,
            letterSpacing: '-0.01em',
        },
        h3: {
            fontSize: '1.75rem',
            fontWeight: 600,
            lineHeight: 1.4,
        },
        h4: {
            fontSize: '1.5rem',
            fontWeight: 600,
            lineHeight: 1.4,
        },
        h5: {
            fontSize: '1.25rem',
            fontWeight: 600,
            lineHeight: 1.5,
        },
        h6: {
            fontSize: '1.125rem',
            fontWeight: 600,
            lineHeight: 1.5,
        },
        body1: {
            fontSize: '1rem',
            lineHeight: 1.6,
        },
        body2: {
            fontSize: '0.875rem',
            lineHeight: 1.6,
        },
        button: {
            textTransform: 'none',
            fontWeight: 600,
        },
    },
    shape: {
        borderRadius: 12,
    },
    spacing: (factor: number) => `${spacing.xs * factor}px`,
};

// Component Customizations
const componentOverrides: ThemeOptions['components'] = {
    MuiCard: {
        styleOverrides: {
            root: {
                boxShadow: shadows.card,
                borderRadius: 16,
                transition: `all ${animations.duration.standard}ms ${animations.easing.cosmic}`,
                '&:hover': {
                    boxShadow: shadows.hover,
                    transform: 'translateY(-2px)',
                },
            },
        },
    },
    MuiButton: {
        styleOverrides: {
            root: {
                borderRadius: 12,
                padding: '12px 24px',
                fontSize: '0.875rem',
                fontWeight: 600,
                textTransform: 'none',
                boxShadow: 'none',
                transition: `all ${animations.duration.short}ms ${animations.easing.cosmic}`,
                '&:hover': {
                    boxShadow: shadows.active,
                    transform: 'translateY(-1px)',
                },
            },
            contained: {
                '&:hover': {
                    boxShadow: shadows.hover,
                },
            },
        },
    },
    MuiChip: {
        styleOverrides: {
            root: {
                borderRadius: 8,
                fontWeight: 500,
                transition: `all ${animations.duration.short}ms ${animations.easing.cosmic}`,
                '&:hover': {
                    transform: 'scale(1.05)',
                },
            },
        },
    },
    MuiPaper: {
        styleOverrides: {
            root: {
                borderRadius: 16,
                boxShadow: shadows.card,
            },
            elevation1: {
                boxShadow: shadows.card,
            },
            elevation4: {
                boxShadow: shadows.cosmic,
            },
        },
    },
    MuiAppBar: {
        styleOverrides: {
            root: {
                boxShadow: '0 1px 3px rgba(0, 0, 0, 0.1)',
                backdropFilter: 'blur(8px)',
            },
        },
    },
    MuiTab: {
        styleOverrides: {
            root: {
                textTransform: 'none',
                fontWeight: 600,
                fontSize: '0.875rem',
                minHeight: 48,
                transition: `all ${animations.duration.short}ms ${animations.easing.cosmic}`,
                '&:hover': {
                    backgroundColor: 'rgba(255, 255, 255, 0.1)',
                },
            },
        },
    },
    MuiTextField: {
        styleOverrides: {
            root: {
                '& .MuiOutlinedInput-root': {
                    borderRadius: 12,
                    transition: `all ${animations.duration.short}ms ${animations.easing.cosmic}`,
                    '&:hover': {
                        '& .MuiOutlinedInput-notchedOutline': {
                            borderColor: hvacColors.primary.light,
                        },
                    },
                    '&.Mui-focused': {
                        '& .MuiOutlinedInput-notchedOutline': {
                            borderWidth: 2,
                        },
                    },
                },
            },
        },
    },
};

// Create Cosmic Theme
export const cosmicTheme = createTheme(deepmerge(baseTheme, {
    components: componentOverrides,
}));

// Export utilities
export { hvacColors, spacing, animations, shadows, PHI };

// Theme variants
export const createCosmicTheme = (mode: 'light' | 'dark' = 'light') => {
    const isDark = mode === 'dark';
    
    return createTheme(deepmerge(baseTheme, {
        palette: {
            mode,
            background: {
                default: isDark ? '#0a0a0a' : '#fafafa',
                paper: isDark ? '#1a1a1a' : '#ffffff',
            },
            text: {
                primary: isDark ? 'rgba(255, 255, 255, 0.87)' : 'rgba(0, 0, 0, 0.87)',
                secondary: isDark ? 'rgba(255, 255, 255, 0.6)' : 'rgba(0, 0, 0, 0.6)',
            },
        },
        components: componentOverrides,
    }));
};
