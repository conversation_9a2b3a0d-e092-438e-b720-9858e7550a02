import {
    List,
    Datagrid,
    TextField,
    DateField,
    ReferenceField,
    NumberField,
    FunctionField,
    EditButton,
    ShowButton,
    DeleteButton,
    TopToolbar,
    CreateButton,
    ExportButton,
    FilterButton,
    SearchInput,
    SelectInput,
    DateInput,
    ReferenceInput,
    AutocompleteInput,
} from 'react-admin';
import { Chip, Box } from '@mui/material';
import { CosmicButton } from '../components/cosmic/CosmicButton';

const invoiceFilters = [
    <SearchInput source="q" alwaysOn />,
    <SelectInput
        source="status"
        choices={[
            { id: 'draft', name: 'Draft' },
            { id: 'sent', name: '<PERSON><PERSON>' },
            { id: 'paid', name: 'Paid' },
            { id: 'overdue', name: 'Overdue' },
            { id: 'cancelled', name: 'Cancelled' },
        ]}
    />,
    <ReferenceInput source="company_id" reference="companies">
        <AutocompleteInput optionText="name" />
    </ReferenceInput>,
    <DateInput source="issue_date_gte" label="Issued After" />,
    <DateInput source="issue_date_lte" label="Issued Before" />,
    <DateInput source="due_date_gte" label="Due After" />,
    <DateInput source="due_date_lte" label="Due Before" />,
];

const InvoiceListActions = () => (
    <TopToolbar>
        <FilterButton />
        <CosmicButton cosmic startIcon={<span>+</span>}>
            New Invoice
        </CosmicButton>
        <ExportButton />
    </TopToolbar>
);

const InvoiceStatusField = ({ record }: { record?: any }) => {
    if (!record) return null;
    
    const statusConfig = {
        draft: { color: '#757575', label: 'Draft' },
        sent: { color: '#2196f3', label: 'Sent' },
        paid: { color: '#4caf50', label: 'Paid' },
        overdue: { color: '#f44336', label: 'Overdue' },
        cancelled: { color: '#ff9800', label: 'Cancelled' },
    };
    
    const config = statusConfig[record.status as keyof typeof statusConfig];
    
    return (
        <Chip
            label={config.label}
            size="small"
            sx={{
                backgroundColor: config.color,
                color: 'white',
                fontWeight: 600,
                ...(record.status === 'overdue' && {
                    animation: 'pulse 2s ease-in-out infinite',
                    '@keyframes pulse': {
                        '0%, 100%': { opacity: 1 },
                        '50%': { opacity: 0.7 },
                    },
                }),
            }}
        />
    );
};

const DueDateField = ({ record }: { record?: any }) => {
    if (!record || !record.due_date) return null;
    
    const today = new Date();
    const dueDate = new Date(record.due_date);
    const isOverdue = dueDate < today && record.status !== 'paid';
    const daysDiff = Math.ceil((dueDate.getTime() - today.getTime()) / (1000 * 60 * 60 * 24));
    
    let color = '#4caf50'; // green for future
    let label = `${daysDiff} days`;
    
    if (isOverdue) {
        color = '#f44336'; // red for overdue
        label = `${Math.abs(daysDiff)} days overdue`;
    } else if (daysDiff <= 7) {
        color = '#ff9800'; // orange for due soon
        label = `${daysDiff} days left`;
    }
    
    return (
        <Box display="flex" flexDirection="column" alignItems="flex-start">
            <DateField source="due_date" record={record} />
            {record.status !== 'paid' && (
                <Chip
                    label={label}
                    size="small"
                    sx={{
                        backgroundColor: color,
                        color: 'white',
                        fontWeight: 500,
                        fontSize: '0.7rem',
                        height: 20,
                        mt: 0.5,
                    }}
                />
            )}
        </Box>
    );
};

const PaymentStatusField = ({ record }: { record?: any }) => {
    if (!record) return null;
    
    if (record.payment_date) {
        return (
            <Chip
                label="Paid"
                size="small"
                sx={{
                    backgroundColor: '#4caf50',
                    color: 'white',
                    fontWeight: 600,
                }}
            />
        );
    }
    
    return (
        <Chip
            label="Unpaid"
            size="small"
            variant="outlined"
            sx={{
                borderColor: '#f44336',
                color: '#f44336',
                fontWeight: 500,
            }}
        />
    );
};

export const InvoiceList = () => (
    <List
        filters={invoiceFilters}
        actions={<InvoiceListActions />}
        sort={{ field: 'created_at', order: 'DESC' }}
        perPage={25}
    >
        <Datagrid rowClick="show" bulkActionButtons={false}>
            <TextField source="invoice_number" />
            <ReferenceField source="company_id" reference="companies" link="show">
                <TextField source="name" />
            </ReferenceField>
            <ReferenceField source="contact_id" reference="contacts" link="show">
                <FunctionField
                    render={(record: any) => `${record.first_name} ${record.last_name}`}
                />
            </ReferenceField>
            <DateField source="issue_date" />
            <FunctionField source="due_date" render={DueDateField} />
            <NumberField 
                source="total_amount" 
                options={{ style: 'currency', currency: 'PLN' }}
            />
            <FunctionField source="status" render={InvoiceStatusField} />
            <FunctionField source="payment_date" render={PaymentStatusField} />
            <ReferenceField source="service_order_id" reference="service_orders" link="show">
                <TextField source="title" />
            </ReferenceField>
            <ShowButton />
            <EditButton />
            <DeleteButton />
        </Datagrid>
    </List>
);
