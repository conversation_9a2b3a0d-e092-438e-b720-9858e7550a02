import React, { useState, useEffect, useMemo } from 'react';
import {
    Box,
    Paper,
    Table,
    TableBody,
    TableCell,
    TableContainer,
    TableHead,
    TableRow,
    TablePagination,
    TableSortLabel,
    TextField,
    InputAdornment,
    IconButton,
    Chip,
    Avatar,
    Checkbox,
    Menu,
    MenuItem,
    Tooltip,
    LinearProgress,
    Typography,
    useTheme,
    alpha,
} from '@mui/material';
import {
    Search,
    FilterList,
    MoreVert,
    GetApp,
    Visibility,
    Edit,
    Delete,
    Star,
    StarBorder,
    TrendingUp,
    TrendingDown,
    Remove,
} from '@mui/icons-material';
import { hvacColors, spacing, animations, PHI } from '../../theme/CosmicTheme';

interface Column {
    id: string;
    label: string;
    minWidth?: number;
    align?: 'right' | 'left' | 'center';
    format?: (value: any) => string;
    sortable?: boolean;
    filterable?: boolean;
    type?: 'text' | 'number' | 'date' | 'status' | 'avatar' | 'progress' | 'trend';
}

interface Row {
    id: string;
    [key: string]: any;
}

interface CosmicDataGridProps {
    columns: Column[];
    rows: Row[];
    title?: string;
    searchable?: boolean;
    filterable?: boolean;
    selectable?: boolean;
    exportable?: boolean;
    onRowClick?: (row: Row) => void;
    onRowSelect?: (selectedRows: Row[]) => void;
    onRowAction?: (action: string, row: Row) => void;
    loading?: boolean;
    emptyMessage?: string;
    aiSuggestions?: boolean;
    healthScoring?: boolean;
}

const CosmicDataGrid: React.FC<CosmicDataGridProps> = ({
    columns,
    rows,
    title,
    searchable = true,
    filterable = true,
    selectable = false,
    exportable = false,
    onRowClick,
    onRowSelect,
    onRowAction,
    loading = false,
    emptyMessage = "No data available",
    aiSuggestions = false,
    healthScoring = false,
}) => {
    const theme = useTheme();
    const [page, setPage] = useState(0);
    const [rowsPerPage, setRowsPerPage] = useState(25);
    const [orderBy, setOrderBy] = useState<string>('');
    const [order, setOrder] = useState<'asc' | 'desc'>('asc');
    const [searchTerm, setSearchTerm] = useState('');
    const [selectedRows, setSelectedRows] = useState<string[]>([]);
    const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);
    const [activeRow, setActiveRow] = useState<Row | null>(null);

    // Filter and sort data
    const filteredAndSortedRows = useMemo(() => {
        let filtered = rows;

        // Apply search filter
        if (searchTerm) {
            filtered = filtered.filter(row =>
                Object.values(row).some(value =>
                    String(value).toLowerCase().includes(searchTerm.toLowerCase())
                )
            );
        }

        // Apply sorting
        if (orderBy) {
            filtered = [...filtered].sort((a, b) => {
                const aValue = a[orderBy];
                const bValue = b[orderBy];
                
                if (aValue < bValue) {
                    return order === 'asc' ? -1 : 1;
                }
                if (aValue > bValue) {
                    return order === 'asc' ? 1 : -1;
                }
                return 0;
            });
        }

        return filtered;
    }, [rows, searchTerm, orderBy, order]);

    const handleSort = (columnId: string) => {
        const isAsc = orderBy === columnId && order === 'asc';
        setOrder(isAsc ? 'desc' : 'asc');
        setOrderBy(columnId);
    };

    const handleSelectAll = (event: React.ChangeEvent<HTMLInputElement>) => {
        if (event.target.checked) {
            const newSelected = filteredAndSortedRows.map(row => row.id);
            setSelectedRows(newSelected);
            onRowSelect?.(filteredAndSortedRows);
        } else {
            setSelectedRows([]);
            onRowSelect?.([]);
        }
    };

    const handleRowSelect = (rowId: string) => {
        const selectedIndex = selectedRows.indexOf(rowId);
        let newSelected: string[] = [];

        if (selectedIndex === -1) {
            newSelected = newSelected.concat(selectedRows, rowId);
        } else if (selectedIndex === 0) {
            newSelected = newSelected.concat(selectedRows.slice(1));
        } else if (selectedIndex === selectedRows.length - 1) {
            newSelected = newSelected.concat(selectedRows.slice(0, -1));
        } else if (selectedIndex > 0) {
            newSelected = newSelected.concat(
                selectedRows.slice(0, selectedIndex),
                selectedRows.slice(selectedIndex + 1),
            );
        }

        setSelectedRows(newSelected);
        const selectedRowObjects = filteredAndSortedRows.filter(row => newSelected.includes(row.id));
        onRowSelect?.(selectedRowObjects);
    };

    const renderCellContent = (column: Column, value: any, row: Row) => {
        switch (column.type) {
            case 'avatar':
                return (
                    <Avatar 
                        src={value} 
                        sx={{ 
                            width: 32, 
                            height: 32,
                            border: `2px solid ${alpha(hvacColors.primary.main, 0.2)}`,
                        }}
                    >
                        {row.name?.charAt(0) || '?'}
                    </Avatar>
                );
            
            case 'status':
                const statusColors = {
                    active: hvacColors.success.main,
                    inactive: hvacColors.error.main,
                    pending: hvacColors.warning.main,
                    completed: hvacColors.success.main,
                    cancelled: hvacColors.error.main,
                };
                return (
                    <Chip
                        label={value}
                        size="small"
                        sx={{
                            backgroundColor: alpha(statusColors[value] || hvacColors.info.main, 0.1),
                            color: statusColors[value] || hvacColors.info.main,
                            fontWeight: 600,
                            borderRadius: 2,
                        }}
                    />
                );
            
            case 'progress':
                return (
                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, minWidth: 100 }}>
                        <LinearProgress
                            variant="determinate"
                            value={value}
                            sx={{
                                flex: 1,
                                height: 6,
                                borderRadius: 3,
                                backgroundColor: alpha(hvacColors.primary.main, 0.1),
                                '& .MuiLinearProgress-bar': {
                                    borderRadius: 3,
                                    background: `linear-gradient(90deg, ${hvacColors.primary.main} 0%, ${hvacColors.secondary.main} 100%)`,
                                },
                            }}
                        />
                        <Typography variant="caption" sx={{ minWidth: 35, textAlign: 'right' }}>
                            {value}%
                        </Typography>
                    </Box>
                );
            
            case 'trend':
                const isPositive = value > 0;
                const isNegative = value < 0;
                return (
                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}>
                        {isPositive && <TrendingUp sx={{ color: hvacColors.success.main, fontSize: 16 }} />}
                        {isNegative && <TrendingDown sx={{ color: hvacColors.error.main, fontSize: 16 }} />}
                        {!isPositive && !isNegative && <Remove sx={{ color: theme.palette.text.secondary, fontSize: 16 }} />}
                        <Typography 
                            variant="body2" 
                            sx={{ 
                                color: isPositive ? hvacColors.success.main : isNegative ? hvacColors.error.main : theme.palette.text.secondary,
                                fontWeight: 600,
                            }}
                        >
                            {isPositive ? '+' : ''}{value}%
                        </Typography>
                    </Box>
                );
            
            default:
                return column.format ? column.format(value) : value;
        }
    };

    const handleMenuOpen = (event: React.MouseEvent<HTMLElement>, row: Row) => {
        event.stopPropagation();
        setAnchorEl(event.currentTarget);
        setActiveRow(row);
    };

    const handleMenuClose = () => {
        setAnchorEl(null);
        setActiveRow(null);
    };

    const handleMenuAction = (action: string) => {
        if (activeRow) {
            onRowAction?.(action, activeRow);
        }
        handleMenuClose();
    };

    return (
        <Paper 
            sx={{ 
                borderRadius: 3,
                overflow: 'hidden',
                boxShadow: `0 8px 32px ${alpha(theme.palette.common.black, 0.08)}`,
            }}
        >
            {/* Header */}
            {(title || searchable || filterable || exportable) && (
                <Box sx={{ 
                    p: spacing.md, 
                    borderBottom: `1px solid ${alpha(theme.palette.divider, 0.1)}`,
                    background: `linear-gradient(135deg, ${alpha(hvacColors.primary.main, 0.02)} 0%, ${alpha(hvacColors.secondary.main, 0.02)} 100%)`,
                }}>
                    <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', mb: title ? 2 : 0 }}>
                        {title && (
                            <Typography variant="h6" sx={{ fontWeight: 600, color: theme.palette.text.primary }}>
                                {title}
                            </Typography>
                        )}
                        <Box sx={{ display: 'flex', gap: 1 }}>
                            {exportable && (
                                <Tooltip title="Export Data">
                                    <IconButton size="small">
                                        <GetApp />
                                    </IconButton>
                                </Tooltip>
                            )}
                            {filterable && (
                                <Tooltip title="Filter">
                                    <IconButton size="small">
                                        <FilterList />
                                    </IconButton>
                                </Tooltip>
                            )}
                        </Box>
                    </Box>
                    
                    {searchable && (
                        <TextField
                            fullWidth
                            size="small"
                            placeholder="Search..."
                            value={searchTerm}
                            onChange={(e) => setSearchTerm(e.target.value)}
                            InputProps={{
                                startAdornment: (
                                    <InputAdornment position="start">
                                        <Search sx={{ color: theme.palette.text.secondary }} />
                                    </InputAdornment>
                                ),
                            }}
                            sx={{
                                '& .MuiOutlinedInput-root': {
                                    borderRadius: 2,
                                    backgroundColor: alpha(theme.palette.background.paper, 0.8),
                                },
                            }}
                        />
                    )}
                </Box>
            )}

            {/* Loading */}
            {loading && (
                <LinearProgress 
                    sx={{
                        '& .MuiLinearProgress-bar': {
                            background: `linear-gradient(90deg, ${hvacColors.primary.main} 0%, ${hvacColors.secondary.main} 100%)`,
                        },
                    }}
                />
            )}

            {/* Table */}
            <TableContainer sx={{ maxHeight: 600 }}>
                <Table stickyHeader>
                    <TableHead>
                        <TableRow>
                            {selectable && (
                                <TableCell padding="checkbox">
                                    <Checkbox
                                        indeterminate={selectedRows.length > 0 && selectedRows.length < filteredAndSortedRows.length}
                                        checked={filteredAndSortedRows.length > 0 && selectedRows.length === filteredAndSortedRows.length}
                                        onChange={handleSelectAll}
                                        sx={{
                                            color: hvacColors.primary.main,
                                            '&.Mui-checked': {
                                                color: hvacColors.primary.main,
                                            },
                                        }}
                                    />
                                </TableCell>
                            )}
                            {columns.map((column) => (
                                <TableCell
                                    key={column.id}
                                    align={column.align}
                                    style={{ minWidth: column.minWidth }}
                                    sx={{
                                        fontWeight: 600,
                                        backgroundColor: alpha(hvacColors.primary.main, 0.02),
                                        borderBottom: `2px solid ${alpha(hvacColors.primary.main, 0.1)}`,
                                    }}
                                >
                                    {column.sortable ? (
                                        <TableSortLabel
                                            active={orderBy === column.id}
                                            direction={orderBy === column.id ? order : 'asc'}
                                            onClick={() => handleSort(column.id)}
                                            sx={{
                                                '&.Mui-active': {
                                                    color: hvacColors.primary.main,
                                                },
                                                '&:hover': {
                                                    color: hvacColors.primary.main,
                                                },
                                            }}
                                        >
                                            {column.label}
                                        </TableSortLabel>
                                    ) : (
                                        column.label
                                    )}
                                </TableCell>
                            ))}
                            <TableCell align="center" sx={{ width: 60 }}>Actions</TableCell>
                        </TableRow>
                    </TableHead>
                    <TableBody>
                        {filteredAndSortedRows
                            .slice(page * rowsPerPage, page * rowsPerPage + rowsPerPage)
                            .map((row) => {
                                const isSelected = selectedRows.indexOf(row.id) !== -1;
                                return (
                                    <TableRow
                                        hover
                                        key={row.id}
                                        selected={isSelected}
                                        onClick={() => onRowClick?.(row)}
                                        sx={{
                                            cursor: onRowClick ? 'pointer' : 'default',
                                            transition: `all ${animations.duration.short}ms ${animations.easing.cosmic}`,
                                            '&:hover': {
                                                backgroundColor: alpha(hvacColors.primary.main, 0.04),
                                            },
                                            '&.Mui-selected': {
                                                backgroundColor: alpha(hvacColors.primary.main, 0.08),
                                                '&:hover': {
                                                    backgroundColor: alpha(hvacColors.primary.main, 0.12),
                                                },
                                            },
                                        }}
                                    >
                                        {selectable && (
                                            <TableCell padding="checkbox">
                                                <Checkbox
                                                    checked={isSelected}
                                                    onChange={() => handleRowSelect(row.id)}
                                                    onClick={(e) => e.stopPropagation()}
                                                    sx={{
                                                        color: hvacColors.primary.main,
                                                        '&.Mui-checked': {
                                                            color: hvacColors.primary.main,
                                                        },
                                                    }}
                                                />
                                            </TableCell>
                                        )}
                                        {columns.map((column) => (
                                            <TableCell key={column.id} align={column.align}>
                                                {renderCellContent(column, row[column.id], row)}
                                            </TableCell>
                                        ))}
                                        <TableCell align="center">
                                            <IconButton
                                                size="small"
                                                onClick={(e) => handleMenuOpen(e, row)}
                                                sx={{
                                                    color: theme.palette.text.secondary,
                                                    '&:hover': {
                                                        color: hvacColors.primary.main,
                                                        backgroundColor: alpha(hvacColors.primary.main, 0.1),
                                                    },
                                                }}
                                            >
                                                <MoreVert />
                                            </IconButton>
                                        </TableCell>
                                    </TableRow>
                                );
                            })}
                    </TableBody>
                </Table>
            </TableContainer>

            {/* Pagination */}
            <TablePagination
                rowsPerPageOptions={[10, 25, 50, 100]}
                component="div"
                count={filteredAndSortedRows.length}
                rowsPerPage={rowsPerPage}
                page={page}
                onPageChange={(_, newPage) => setPage(newPage)}
                onRowsPerPageChange={(e) => {
                    setRowsPerPage(parseInt(e.target.value, 10));
                    setPage(0);
                }}
                sx={{
                    borderTop: `1px solid ${alpha(theme.palette.divider, 0.1)}`,
                    backgroundColor: alpha(hvacColors.primary.main, 0.01),
                }}
            />

            {/* Context Menu */}
            <Menu
                anchorEl={anchorEl}
                open={Boolean(anchorEl)}
                onClose={handleMenuClose}
                PaperProps={{
                    sx: {
                        borderRadius: 2,
                        boxShadow: `0 8px 32px ${alpha(theme.palette.common.black, 0.12)}`,
                    },
                }}
            >
                <MenuItem onClick={() => handleMenuAction('view')}>
                    <Visibility sx={{ mr: 1, fontSize: 18 }} />
                    View Details
                </MenuItem>
                <MenuItem onClick={() => handleMenuAction('edit')}>
                    <Edit sx={{ mr: 1, fontSize: 18 }} />
                    Edit
                </MenuItem>
                <MenuItem onClick={() => handleMenuAction('delete')}>
                    <Delete sx={{ mr: 1, fontSize: 18, color: hvacColors.error.main }} />
                    Delete
                </MenuItem>
            </Menu>
        </Paper>
    );
};

export default CosmicDataGrid;
