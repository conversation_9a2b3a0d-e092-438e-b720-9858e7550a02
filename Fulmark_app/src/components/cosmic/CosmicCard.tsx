import React from 'react';
import {
    Card,
    CardContent,
    CardActions,
    Box,
    Typography,
    IconButton,
    Fade,
    Grow,
    useTheme,
    alpha,
} from '@mui/material';
import { styled, keyframes } from '@mui/material/styles';
import { MoreVert as MoreIcon } from '@mui/icons-material';

// Cosmic animations
const shimmer = keyframes`
    0% {
        background-position: -200px 0;
    }
    100% {
        background-position: calc(200px + 100%) 0;
    }
`;

const pulse = keyframes`
    0% {
        transform: scale(1);
        opacity: 1;
    }
    50% {
        transform: scale(1.05);
        opacity: 0.8;
    }
    100% {
        transform: scale(1);
        opacity: 1;
    }
`;

const float = keyframes`
    0%, 100% {
        transform: translateY(0px);
    }
    50% {
        transform: translateY(-4px);
    }
`;

// Styled Components
const StyledCard = styled(Card, {
    shouldForwardProp: (prop) => !['variant', 'interactive', 'loading'].includes(prop as string),
})<{
    variant?: 'default' | 'gradient' | 'glass' | 'elevated';
    interactive?: boolean;
    loading?: boolean;
}>(({ theme, variant = 'default', interactive = false, loading = false }) => ({
    position: 'relative',
    overflow: 'hidden',
    transition: 'all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94)',
    cursor: interactive ? 'pointer' : 'default',
    
    // Variant styles
    ...(variant === 'gradient' && {
        background: `linear-gradient(135deg, ${theme.palette.primary.main} 0%, ${theme.palette.secondary.main} 100%)`,
        color: theme.palette.primary.contrastText,
    }),
    
    ...(variant === 'glass' && {
        background: alpha(theme.palette.background.paper, 0.8),
        backdropFilter: 'blur(10px)',
        border: `1px solid ${alpha(theme.palette.primary.main, 0.1)}`,
    }),
    
    ...(variant === 'elevated' && {
        boxShadow: '0 8px 32px rgba(0, 0, 0, 0.12)',
    }),
    
    // Interactive states
    ...(interactive && {
        '&:hover': {
            transform: 'translateY(-4px) scale(1.02)',
            boxShadow: '0 12px 40px rgba(0, 0, 0, 0.15)',
            '& .cosmic-card-actions': {
                opacity: 1,
                transform: 'translateY(0)',
            },
        },
        '&:active': {
            transform: 'translateY(-2px) scale(1.01)',
        },
    }),
    
    // Loading state
    ...(loading && {
        '&::before': {
            content: '""',
            position: 'absolute',
            top: 0,
            left: 0,
            right: 0,
            bottom: 0,
            background: `linear-gradient(
                90deg,
                transparent,
                ${alpha(theme.palette.primary.main, 0.1)},
                transparent
            )`,
            animation: `${shimmer} 2s infinite`,
            zIndex: 1,
        },
    }),
    
    // Floating animation for special cards
    '&.cosmic-float': {
        animation: `${float} 3s ease-in-out infinite`,
    },
    
    // Pulse animation for urgent items
    '&.cosmic-pulse': {
        animation: `${pulse} 2s ease-in-out infinite`,
    },
}));

const StyledCardActions = styled(CardActions)(({ theme }) => ({
    opacity: 0,
    transform: 'translateY(10px)',
    transition: 'all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94)',
    padding: theme.spacing(1, 2),
    justifyContent: 'flex-end',
}));

const GlowEffect = styled(Box)(({ theme }) => ({
    position: 'absolute',
    top: -2,
    left: -2,
    right: -2,
    bottom: -2,
    background: `linear-gradient(45deg, ${theme.palette.primary.main}, ${theme.palette.secondary.main})`,
    borderRadius: 'inherit',
    zIndex: -1,
    opacity: 0,
    transition: 'opacity 0.3s ease',
    
    '.cosmic-card:hover &': {
        opacity: 0.3,
    },
}));

// Component Props
interface CosmicCardProps {
    children: React.ReactNode;
    title?: string;
    subtitle?: string;
    variant?: 'default' | 'gradient' | 'glass' | 'elevated';
    interactive?: boolean;
    loading?: boolean;
    urgent?: boolean;
    floating?: boolean;
    glow?: boolean;
    actions?: React.ReactNode;
    onClick?: () => void;
    className?: string;
    sx?: any;
}

export const CosmicCard: React.FC<CosmicCardProps> = ({
    children,
    title,
    subtitle,
    variant = 'default',
    interactive = false,
    loading = false,
    urgent = false,
    floating = false,
    glow = false,
    actions,
    onClick,
    className = '',
    sx = {},
}) => {
    const theme = useTheme();
    
    const cardClasses = [
        className,
        urgent && 'cosmic-pulse',
        floating && 'cosmic-float',
        'cosmic-card',
    ].filter(Boolean).join(' ');

    return (
        <Grow in timeout={500}>
            <Box position="relative">
                {glow && <GlowEffect />}
                <StyledCard
                    variant={variant}
                    interactive={interactive}
                    loading={loading}
                    onClick={onClick}
                    className={cardClasses}
                    sx={sx}
                >
                    {loading && (
                        <Box
                            position="absolute"
                            top={0}
                            left={0}
                            right={0}
                            height={4}
                            bgcolor="primary.main"
                            sx={{
                                background: `linear-gradient(90deg, 
                                    ${theme.palette.primary.main} 0%, 
                                    ${theme.palette.secondary.main} 50%, 
                                    ${theme.palette.primary.main} 100%)`,
                                backgroundSize: '200% 100%',
                                animation: `${shimmer} 2s infinite`,
                            }}
                        />
                    )}
                    
                    <CardContent>
                        {(title || subtitle) && (
                            <Box mb={2}>
                                {title && (
                                    <Typography
                                        variant="h6"
                                        component="h2"
                                        gutterBottom={!!subtitle}
                                        sx={{
                                            fontWeight: 600,
                                            color: variant === 'gradient' ? 'inherit' : 'text.primary',
                                        }}
                                    >
                                        {title}
                                    </Typography>
                                )}
                                {subtitle && (
                                    <Typography
                                        variant="body2"
                                        color={variant === 'gradient' ? 'inherit' : 'text.secondary'}
                                        sx={{ opacity: 0.8 }}
                                    >
                                        {subtitle}
                                    </Typography>
                                )}
                            </Box>
                        )}
                        
                        {children}
                    </CardContent>
                    
                    {actions && (
                        <StyledCardActions className="cosmic-card-actions">
                            {actions}
                        </StyledCardActions>
                    )}
                    
                    {interactive && !actions && (
                        <StyledCardActions className="cosmic-card-actions">
                            <IconButton size="small" sx={{ color: 'inherit' }}>
                                <MoreIcon />
                            </IconButton>
                        </StyledCardActions>
                    )}
                </StyledCard>
            </Box>
        </Grow>
    );
};

// Specialized Card Variants
export const CosmicMetricCard: React.FC<{
    title: string;
    value: string | number;
    subtitle?: string;
    trend?: 'up' | 'down' | 'neutral';
    trendValue?: string;
    icon?: React.ReactNode;
    color?: 'primary' | 'secondary' | 'success' | 'warning' | 'error';
    loading?: boolean;
}> = ({
    title,
    value,
    subtitle,
    trend,
    trendValue,
    icon,
    color = 'primary',
    loading = false,
}) => {
    const theme = useTheme();
    
    const trendColors = {
        up: theme.palette.success.main,
        down: theme.palette.error.main,
        neutral: theme.palette.text.secondary,
    };

    return (
        <CosmicCard
            variant="elevated"
            loading={loading}
            sx={{
                background: `linear-gradient(135deg, ${theme.palette[color].main}15 0%, ${theme.palette[color].main}05 100%)`,
                border: `1px solid ${alpha(theme.palette[color].main, 0.1)}`,
            }}
        >
            <Box display="flex" alignItems="center" justifyContent="space-between" mb={2}>
                <Typography variant="body2" color="text.secondary" fontWeight={500}>
                    {title}
                </Typography>
                {icon && (
                    <Box sx={{ color: theme.palette[color].main, fontSize: 24 }}>
                        {icon}
                    </Box>
                )}
            </Box>
            
            <Typography variant="h4" fontWeight={700} color="text.primary" mb={1}>
                {loading ? '---' : value}
            </Typography>
            
            {(subtitle || trend) && (
                <Box display="flex" alignItems="center" gap={1}>
                    {subtitle && (
                        <Typography variant="body2" color="text.secondary">
                            {subtitle}
                        </Typography>
                    )}
                    {trend && trendValue && (
                        <Typography
                            variant="body2"
                            sx={{
                                color: trendColors[trend],
                                fontWeight: 600,
                                display: 'flex',
                                alignItems: 'center',
                                gap: 0.5,
                            }}
                        >
                            {trend === 'up' ? '↗' : trend === 'down' ? '↘' : '→'} {trendValue}
                        </Typography>
                    )}
                </Box>
            )}
        </CosmicCard>
    );
};
