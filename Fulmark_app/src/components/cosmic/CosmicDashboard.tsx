import React, { useState, useEffect } from 'react';
import {
    <PERSON>,
    Grid,
    Card,
    CardContent,
    Typography,
    IconButton,
    Chip,
    LinearProgress,
    Avatar,
    Badge,
    Tooltip,
    Fab,
    SpeedDial,
    SpeedDialAction,
    SpeedDialIcon,
    useTheme,
    alpha,
} from '@mui/material';
import {
    Dashboard as DashboardIcon,
    TrendingUp,
    TrendingDown,
    People,
    Build,
    Assignment,
    AttachMoney,
    Notifications,
    Add,
    Person,
    Settings,
    Analytics,
    Schedule,
    Warning,
    CheckCircle,
    Error,
    Info,
} from '@mui/icons-material';
import { CosmicCard, CosmicMetricCard } from './CosmicCard';
import { hvacColors, spacing, animations, PHI } from '../../theme/CosmicTheme';

interface DashboardMetric {
    id: string;
    title: string;
    value: string | number;
    change: number;
    changeType: 'increase' | 'decrease' | 'neutral';
    icon: React.ReactNode;
    color: string;
    subtitle?: string;
}

interface DashboardAlert {
    id: string;
    type: 'error' | 'warning' | 'info' | 'success';
    title: string;
    message: string;
    timestamp: Date;
    actionLabel?: string;
    onAction?: () => void;
}

interface CosmicDashboardProps {
    module: 'crm' | 'service' | 'equipment' | 'quotes' | 'overview';
    metrics: DashboardMetric[];
    alerts?: DashboardAlert[];
    onMetricClick?: (metricId: string) => void;
    onQuickAction?: (action: string) => void;
    children?: React.ReactNode;
}

const CosmicDashboard: React.FC<CosmicDashboardProps> = ({
    module,
    metrics,
    alerts = [],
    onMetricClick,
    onQuickAction,
    children,
}) => {
    const theme = useTheme();
    const [selectedMetric, setSelectedMetric] = useState<string | null>(null);

    // Quick actions based on module
    const getQuickActions = () => {
        const baseActions = [
            { icon: <Add />, name: 'Add New', action: 'add' },
            { icon: <Analytics />, name: 'Analytics', action: 'analytics' },
            { icon: <Settings />, name: 'Settings', action: 'settings' },
        ];

        switch (module) {
            case 'crm':
                return [
                    { icon: <Person />, name: 'New Customer', action: 'new-customer' },
                    { icon: <Schedule />, name: 'Schedule Call', action: 'schedule-call' },
                    ...baseActions,
                ];
            case 'service':
                return [
                    { icon: <Assignment />, name: 'New Order', action: 'new-order' },
                    { icon: <Build />, name: 'Assign Tech', action: 'assign-tech' },
                    ...baseActions,
                ];
            case 'equipment':
                return [
                    { icon: <Build />, name: 'Add Equipment', action: 'add-equipment' },
                    { icon: <Schedule />, name: 'Schedule Maintenance', action: 'schedule-maintenance' },
                    ...baseActions,
                ];
            case 'quotes':
                return [
                    { icon: <AttachMoney />, name: 'New Quote', action: 'new-quote' },
                    { icon: <TrendingUp />, name: 'Price Analysis', action: 'price-analysis' },
                    ...baseActions,
                ];
            default:
                return baseActions;
        }
    };

    const getAlertIcon = (type: string) => {
        switch (type) {
            case 'error': return <Error color="error" />;
            case 'warning': return <Warning color="warning" />;
            case 'success': return <CheckCircle color="success" />;
            default: return <Info color="info" />;
        }
    };

    const getAlertColor = (type: string) => {
        switch (type) {
            case 'error': return theme.palette.error.main;
            case 'warning': return theme.palette.warning.main;
            case 'success': return theme.palette.success.main;
            default: return theme.palette.info.main;
        }
    };

    return (
        <Box sx={{ 
            p: spacing.md,
            minHeight: '100vh',
            background: `linear-gradient(135deg, ${alpha(hvacColors.primary.main, 0.02)} 0%, ${alpha(hvacColors.secondary.main, 0.02)} 100%)`,
        }}>
            {/* Header */}
            <Box sx={{ mb: spacing.lg }}>
                <Typography 
                    variant="h4" 
                    sx={{ 
                        fontWeight: 700,
                        color: theme.palette.text.primary,
                        mb: 1,
                        display: 'flex',
                        alignItems: 'center',
                        gap: 2,
                    }}
                >
                    <DashboardIcon sx={{ fontSize: 32, color: hvacColors.primary.main }} />
                    {module.charAt(0).toUpperCase() + module.slice(1)} Dashboard
                </Typography>
                <Typography variant="body1" color="text.secondary">
                    Real-time insights and cosmic-level performance metrics
                </Typography>
            </Box>

            {/* Alerts Section */}
            {alerts.length > 0 && (
                <Box sx={{ mb: spacing.lg }}>
                    <Typography variant="h6" sx={{ mb: spacing.sm, fontWeight: 600 }}>
                        Active Alerts
                    </Typography>
                    <Grid container spacing={spacing.sm}>
                        {alerts.slice(0, 3).map((alert) => (
                            <Grid item xs={12} md={4} key={alert.id}>
                                <Card 
                                    sx={{ 
                                        borderLeft: `4px solid ${getAlertColor(alert.type)}`,
                                        transition: `all ${animations.duration.standard}ms ${animations.easing.cosmic}`,
                                        '&:hover': {
                                            transform: 'translateY(-2px)',
                                            boxShadow: theme.shadows[8],
                                        },
                                    }}
                                >
                                    <CardContent sx={{ p: spacing.md }}>
                                        <Box sx={{ display: 'flex', alignItems: 'flex-start', gap: 2 }}>
                                            {getAlertIcon(alert.type)}
                                            <Box sx={{ flex: 1 }}>
                                                <Typography variant="subtitle2" sx={{ fontWeight: 600, mb: 0.5 }}>
                                                    {alert.title}
                                                </Typography>
                                                <Typography variant="body2" color="text.secondary" sx={{ mb: 1 }}>
                                                    {alert.message}
                                                </Typography>
                                                <Typography variant="caption" color="text.secondary">
                                                    {alert.timestamp.toLocaleTimeString()}
                                                </Typography>
                                            </Box>
                                        </Box>
                                    </CardContent>
                                </Card>
                            </Grid>
                        ))}
                    </Grid>
                </Box>
            )}

            {/* Metrics Grid */}
            <Grid container spacing={spacing.md} sx={{ mb: spacing.lg }}>
                {metrics.map((metric) => (
                    <Grid item xs={12} sm={6} md={3} key={metric.id}>
                        <CosmicMetricCard
                            title={metric.title}
                            value={metric.value}
                            change={metric.change}
                            changeType={metric.changeType}
                            icon={metric.icon}
                            color={metric.color}
                            subtitle={metric.subtitle}
                            onClick={() => {
                                setSelectedMetric(metric.id);
                                onMetricClick?.(metric.id);
                            }}
                            selected={selectedMetric === metric.id}
                        />
                    </Grid>
                ))}
            </Grid>

            {/* Main Content */}
            <Box sx={{ mb: spacing.xl }}>
                {children}
            </Box>

            {/* Quick Actions Speed Dial */}
            <SpeedDial
                ariaLabel="Quick Actions"
                sx={{ 
                    position: 'fixed', 
                    bottom: spacing.lg, 
                    right: spacing.lg,
                    '& .MuiFab-primary': {
                        background: `linear-gradient(135deg, ${hvacColors.primary.main} 0%, ${hvacColors.secondary.main} 100%)`,
                        '&:hover': {
                            background: `linear-gradient(135deg, ${hvacColors.primary.dark} 0%, ${hvacColors.secondary.dark} 100%)`,
                        },
                    },
                }}
                icon={<SpeedDialIcon />}
            >
                {getQuickActions().map((action) => (
                    <SpeedDialAction
                        key={action.action}
                        icon={action.icon}
                        tooltipTitle={action.name}
                        onClick={() => onQuickAction?.(action.action)}
                        sx={{
                            '& .MuiFab-primary': {
                                backgroundColor: hvacColors.primary.main,
                                '&:hover': {
                                    backgroundColor: hvacColors.primary.dark,
                                },
                            },
                        }}
                    />
                ))}
            </SpeedDial>
        </Box>
    );
};

export default CosmicDashboard;
