import React, { useState, useEffect } from 'react';
import {
    Box,
    Paper,
    Typography,
    Card,
    CardContent,
    Grid,
    Chip,
    Avatar,
    IconButton,
    Button,
    LinearProgress,
    Tooltip,
    Badge,
    TreeView,
    TreeItem,
    Dialog,
    DialogTitle,
    DialogContent,
    DialogActions,
    List,
    ListItem,
    ListItemIcon,
    ListItemText,
    Divider,
    useTheme,
    alpha,
} from '@mui/material';
import {
    ExpandMore,
    ChevronRight,
    Build,
    Schedule,
    Warning,
    CheckCircle,
    Error,
    Info,
    TrendingUp,
    TrendingDown,
    Thermostat,
    Speed,
    Battery,
    Wifi,
    QrCode,
    Edit,
    Delete,
    Visibility,
    Assignment,
    History,
    Analytics,
    Settings,
    CloudUpload,
    Download,
    Print,
} from '@mui/icons-material';
import { hvacColors, spacing, animations, PHI } from '../../theme/CosmicTheme';

interface Equipment {
    id: string;
    name: string;
    type: 'hvac' | 'boiler' | 'heat_pump' | 'air_conditioner' | 'ventilation';
    brand: string;
    model: string;
    serialNumber: string;
    location: string;
    customerId: string;
    customerName: string;
    installDate: Date;
    warrantyExpiry?: Date;
    status: 'active' | 'maintenance' | 'repair' | 'inactive' | 'decommissioned';
    healthScore: number;
    lastService?: Date;
    nextService?: Date;
    iotConnected: boolean;
    specifications: Record<string, any>;
    maintenanceHistory: MaintenanceRecord[];
    alerts: Alert[];
    documents: Document[];
}

interface MaintenanceRecord {
    id: string;
    date: Date;
    type: 'preventive' | 'corrective' | 'emergency';
    description: string;
    technician: string;
    cost: number;
    partsUsed: string[];
    notes?: string;
}

interface Alert {
    id: string;
    type: 'error' | 'warning' | 'info';
    title: string;
    message: string;
    timestamp: Date;
    resolved: boolean;
    priority: 'low' | 'medium' | 'high' | 'critical';
}

interface Document {
    id: string;
    name: string;
    type: 'manual' | 'warranty' | 'certificate' | 'photo' | 'report';
    url: string;
    uploadDate: Date;
    size: number;
}

interface IoTData {
    temperature: number;
    pressure: number;
    efficiency: number;
    energyConsumption: number;
    operatingHours: number;
    lastUpdate: Date;
}

interface CosmicEquipmentRegistryProps {
    equipment: Equipment[];
    iotData: Record<string, IoTData>;
    onEquipmentSelect?: (equipment: Equipment) => void;
    onScheduleMaintenance?: (equipmentId: string) => void;
    onCreateOrder?: (equipmentId: string) => void;
    onEditEquipment?: (equipmentId: string) => void;
    onDeleteEquipment?: (equipmentId: string) => void;
    onViewDocuments?: (equipmentId: string) => void;
    groupBy?: 'customer' | 'type' | 'location' | 'status';
}

const CosmicEquipmentRegistry: React.FC<CosmicEquipmentRegistryProps> = ({
    equipment,
    iotData,
    onEquipmentSelect,
    onScheduleMaintenance,
    onCreateOrder,
    onEditEquipment,
    onDeleteEquipment,
    onViewDocuments,
    groupBy = 'customer',
}) => {
    const theme = useTheme();
    const [selectedEquipment, setSelectedEquipment] = useState<Equipment | null>(null);
    const [detailsOpen, setDetailsOpen] = useState(false);
    const [expandedNodes, setExpandedNodes] = useState<string[]>([]);

    const getStatusColor = (status: string) => {
        switch (status) {
            case 'active': return hvacColors.success.main;
            case 'maintenance': return hvacColors.warning.main;
            case 'repair': return hvacColors.error.main;
            case 'inactive': return theme.palette.text.disabled;
            case 'decommissioned': return theme.palette.text.disabled;
            default: return theme.palette.text.secondary;
        }
    };

    const getHealthScoreColor = (score: number) => {
        if (score >= 80) return hvacColors.success.main;
        if (score >= 60) return hvacColors.warning.main;
        return hvacColors.error.main;
    };

    const getTypeIcon = (type: string) => {
        switch (type) {
            case 'hvac': return <Thermostat />;
            case 'boiler': return <Build />;
            case 'heat_pump': return <TrendingUp />;
            case 'air_conditioner': return <Speed />;
            case 'ventilation': return <TrendingUp />;
            default: return <Build />;
        }
    };

    const getAlertIcon = (type: string) => {
        switch (type) {
            case 'error': return <Error color="error" />;
            case 'warning': return <Warning color="warning" />;
            case 'info': return <Info color="info" />;
            default: return <Info />;
        }
    };

    const groupEquipment = () => {
        const grouped: Record<string, Equipment[]> = {};
        
        equipment.forEach((item) => {
            let key: string;
            switch (groupBy) {
                case 'customer':
                    key = item.customerName;
                    break;
                case 'type':
                    key = item.type;
                    break;
                case 'location':
                    key = item.location;
                    break;
                case 'status':
                    key = item.status;
                    break;
                default:
                    key = 'All Equipment';
            }
            
            if (!grouped[key]) {
                grouped[key] = [];
            }
            grouped[key].push(item);
        });
        
        return grouped;
    };

    const handleEquipmentClick = (equipment: Equipment) => {
        setSelectedEquipment(equipment);
        setDetailsOpen(true);
        onEquipmentSelect?.(equipment);
    };

    const renderEquipmentCard = (item: Equipment) => {
        const iot = iotData[item.id];
        const activeAlerts = item.alerts.filter(alert => !alert.resolved);
        
        return (
            <Card
                key={item.id}
                onClick={() => handleEquipmentClick(item)}
                sx={{
                    cursor: 'pointer',
                    transition: `all ${animations.duration.standard}ms ${animations.easing.cosmic}`,
                    border: `1px solid ${alpha(theme.palette.divider, 0.1)}`,
                    '&:hover': {
                        transform: 'translateY(-4px)',
                        boxShadow: `0 12px 40px ${alpha(theme.palette.common.black, 0.12)}`,
                        borderColor: alpha(hvacColors.primary.main, 0.3),
                    },
                }}
            >
                <CardContent sx={{ p: spacing.md }}>
                    {/* Header */}
                    <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start', mb: 2 }}>
                        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, flex: 1 }}>
                            <Avatar
                                sx={{
                                    backgroundColor: alpha(getStatusColor(item.status), 0.1),
                                    color: getStatusColor(item.status),
                                    width: 40,
                                    height: 40,
                                }}
                            >
                                {getTypeIcon(item.type)}
                            </Avatar>
                            <Box>
                                <Typography variant="h6" sx={{ fontWeight: 600, mb: 0.5 }}>
                                    {item.brand} {item.model}
                                </Typography>
                                <Typography variant="body2" color="text.secondary">
                                    {item.name} • {item.location}
                                </Typography>
                            </Box>
                        </Box>
                        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                            {item.iotConnected && (
                                <Tooltip title="IoT Connected">
                                    <Wifi sx={{ color: hvacColors.success.main, fontSize: 20 }} />
                                </Tooltip>
                            )}
                            {activeAlerts.length > 0 && (
                                <Badge badgeContent={activeAlerts.length} color="error">
                                    <Warning sx={{ color: hvacColors.warning.main }} />
                                </Badge>
                            )}
                            <Chip
                                label={item.status}
                                size="small"
                                sx={{
                                    backgroundColor: alpha(getStatusColor(item.status), 0.1),
                                    color: getStatusColor(item.status),
                                    fontWeight: 600,
                                }}
                            />
                        </Box>
                    </Box>

                    {/* Health Score */}
                    <Box sx={{ mb: 2 }}>
                        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 1 }}>
                            <Typography variant="subtitle2" sx={{ fontWeight: 600 }}>
                                Health Score
                            </Typography>
                            <Typography variant="subtitle2" sx={{ fontWeight: 700, color: getHealthScoreColor(item.healthScore) }}>
                                {item.healthScore}%
                            </Typography>
                        </Box>
                        <LinearProgress
                            variant="determinate"
                            value={item.healthScore}
                            sx={{
                                height: 6,
                                borderRadius: 3,
                                backgroundColor: alpha(theme.palette.divider, 0.2),
                                '& .MuiLinearProgress-bar': {
                                    borderRadius: 3,
                                    backgroundColor: getHealthScoreColor(item.healthScore),
                                },
                            }}
                        />
                    </Box>

                    {/* IoT Metrics */}
                    {iot && (
                        <Grid container spacing={1} sx={{ mb: 2 }}>
                            <Grid item xs={6}>
                                <Box sx={{ textAlign: 'center', p: 1, backgroundColor: alpha(hvacColors.primary.main, 0.05), borderRadius: 1 }}>
                                    <Typography variant="h6" sx={{ fontWeight: 700, color: hvacColors.primary.main }}>
                                        {iot.temperature}°C
                                    </Typography>
                                    <Typography variant="caption" color="text.secondary">
                                        Temperature
                                    </Typography>
                                </Box>
                            </Grid>
                            <Grid item xs={6}>
                                <Box sx={{ textAlign: 'center', p: 1, backgroundColor: alpha(hvacColors.secondary.main, 0.05), borderRadius: 1 }}>
                                    <Typography variant="h6" sx={{ fontWeight: 700, color: hvacColors.secondary.main }}>
                                        {iot.efficiency}%
                                    </Typography>
                                    <Typography variant="caption" color="text.secondary">
                                        Efficiency
                                    </Typography>
                                </Box>
                            </Grid>
                        </Grid>
                    )}

                    {/* Service Info */}
                    <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', pt: 1, borderTop: `1px solid ${alpha(theme.palette.divider, 0.1)}` }}>
                        <Box>
                            <Typography variant="caption" color="text.secondary">
                                Last Service: {item.lastService ? item.lastService.toLocaleDateString() : 'Never'}
                            </Typography>
                        </Box>
                        <Box>
                            <Typography variant="caption" color="text.secondary">
                                Next Service: {item.nextService ? item.nextService.toLocaleDateString() : 'Not scheduled'}
                            </Typography>
                        </Box>
                    </Box>

                    {/* Quick Actions */}
                    <Box sx={{ display: 'flex', gap: 1, mt: 2 }}>
                        <Button
                            size="small"
                            variant="outlined"
                            startIcon={<Schedule />}
                            onClick={(e) => {
                                e.stopPropagation();
                                onScheduleMaintenance?.(item.id);
                            }}
                            sx={{ flex: 1 }}
                        >
                            Schedule
                        </Button>
                        <Button
                            size="small"
                            variant="outlined"
                            startIcon={<Assignment />}
                            onClick={(e) => {
                                e.stopPropagation();
                                onCreateOrder?.(item.id);
                            }}
                            sx={{ flex: 1 }}
                        >
                            Service
                        </Button>
                    </Box>
                </CardContent>
            </Card>
        );
    };

    const renderTreeView = () => {
        const grouped = groupEquipment();
        
        return (
            <TreeView
                defaultCollapseIcon={<ExpandMore />}
                defaultExpandIcon={<ChevronRight />}
                expanded={expandedNodes}
                onNodeToggle={(_, nodeIds) => setExpandedNodes(nodeIds)}
                sx={{
                    flexGrow: 1,
                    maxWidth: 400,
                    overflowY: 'auto',
                }}
            >
                {Object.entries(grouped).map(([groupName, items]) => (
                    <TreeItem
                        key={groupName}
                        nodeId={groupName}
                        label={
                            <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, py: 1 }}>
                                <Typography variant="subtitle1" sx={{ fontWeight: 600 }}>
                                    {groupName}
                                </Typography>
                                <Chip
                                    label={items.length}
                                    size="small"
                                    sx={{
                                        backgroundColor: alpha(hvacColors.primary.main, 0.1),
                                        color: hvacColors.primary.main,
                                        height: 20,
                                    }}
                                />
                            </Box>
                        }
                    >
                        {items.map((item) => (
                            <TreeItem
                                key={item.id}
                                nodeId={item.id}
                                label={
                                    <Box
                                        onClick={() => handleEquipmentClick(item)}
                                        sx={{
                                            display: 'flex',
                                            alignItems: 'center',
                                            gap: 1,
                                            py: 0.5,
                                            cursor: 'pointer',
                                            '&:hover': {
                                                backgroundColor: alpha(hvacColors.primary.main, 0.05),
                                            },
                                        }}
                                    >
                                        <Avatar
                                            sx={{
                                                backgroundColor: alpha(getStatusColor(item.status), 0.1),
                                                color: getStatusColor(item.status),
                                                width: 24,
                                                height: 24,
                                            }}
                                        >
                                            {getTypeIcon(item.type)}
                                        </Avatar>
                                        <Box sx={{ flex: 1 }}>
                                            <Typography variant="body2" sx={{ fontWeight: 600 }}>
                                                {item.brand} {item.model}
                                            </Typography>
                                            <Typography variant="caption" color="text.secondary">
                                                {item.location} • Health: {item.healthScore}%
                                            </Typography>
                                        </Box>
                                        {item.alerts.filter(a => !a.resolved).length > 0 && (
                                            <Badge
                                                badgeContent={item.alerts.filter(a => !a.resolved).length}
                                                color="error"
                                                sx={{ mr: 1 }}
                                            />
                                        )}
                                    </Box>
                                }
                            />
                        ))}
                    </TreeItem>
                ))}
            </TreeView>
        );
    };

    return (
        <Box sx={{ display: 'flex', height: '100%', gap: spacing.md }}>
            {/* Tree View Sidebar */}
            <Paper
                sx={{
                    width: 400,
                    borderRadius: 3,
                    overflow: 'hidden',
                    boxShadow: `0 4px 16px ${alpha(theme.palette.common.black, 0.08)}`,
                }}
            >
                <Box
                    sx={{
                        p: spacing.md,
                        background: `linear-gradient(135deg, ${alpha(hvacColors.primary.main, 0.1)} 0%, ${alpha(hvacColors.secondary.main, 0.05)} 100%)`,
                        borderBottom: `1px solid ${alpha(theme.palette.divider, 0.1)}`,
                    }}
                >
                    <Typography variant="h6" sx={{ fontWeight: 600, mb: 1 }}>
                        Equipment Registry
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                        {equipment.length} devices • {equipment.filter(e => e.iotConnected).length} IoT connected
                    </Typography>
                </Box>
                <Box sx={{ p: spacing.sm }}>
                    {renderTreeView()}
                </Box>
            </Paper>

            {/* Main Content */}
            <Box sx={{ flex: 1 }}>
                <Grid container spacing={spacing.md}>
                    {equipment.map((item) => (
                        <Grid item xs={12} sm={6} lg={4} key={item.id}>
                            {renderEquipmentCard(item)}
                        </Grid>
                    ))}
                </Grid>
            </Box>

            {/* Equipment Details Dialog */}
            <Dialog
                open={detailsOpen}
                onClose={() => setDetailsOpen(false)}
                maxWidth="md"
                fullWidth
                PaperProps={{
                    sx: {
                        borderRadius: 3,
                        boxShadow: `0 12px 48px ${alpha(theme.palette.common.black, 0.15)}`,
                    },
                }}
            >
                {selectedEquipment && (
                    <>
                        <DialogTitle sx={{ pb: 1 }}>
                            <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                                <Avatar
                                    sx={{
                                        backgroundColor: alpha(getStatusColor(selectedEquipment.status), 0.1),
                                        color: getStatusColor(selectedEquipment.status),
                                        width: 48,
                                        height: 48,
                                    }}
                                >
                                    {getTypeIcon(selectedEquipment.type)}
                                </Avatar>
                                <Box>
                                    <Typography variant="h5" sx={{ fontWeight: 700 }}>
                                        {selectedEquipment.brand} {selectedEquipment.model}
                                    </Typography>
                                    <Typography variant="body2" color="text.secondary">
                                        {selectedEquipment.name} • SN: {selectedEquipment.serialNumber}
                                    </Typography>
                                </Box>
                            </Box>
                        </DialogTitle>
                        <DialogContent>
                            <Grid container spacing={spacing.md}>
                                <Grid item xs={12} md={6}>
                                    <Typography variant="h6" sx={{ mb: 2, fontWeight: 600 }}>
                                        Equipment Details
                                    </Typography>
                                    <List>
                                        <ListItem sx={{ px: 0 }}>
                                            <ListItemText
                                                primary="Customer"
                                                secondary={selectedEquipment.customerName}
                                            />
                                        </ListItem>
                                        <ListItem sx={{ px: 0 }}>
                                            <ListItemText
                                                primary="Location"
                                                secondary={selectedEquipment.location}
                                            />
                                        </ListItem>
                                        <ListItem sx={{ px: 0 }}>
                                            <ListItemText
                                                primary="Install Date"
                                                secondary={selectedEquipment.installDate.toLocaleDateString()}
                                            />
                                        </ListItem>
                                        {selectedEquipment.warrantyExpiry && (
                                            <ListItem sx={{ px: 0 }}>
                                                <ListItemText
                                                    primary="Warranty Expiry"
                                                    secondary={selectedEquipment.warrantyExpiry.toLocaleDateString()}
                                                />
                                            </ListItem>
                                        )}
                                    </List>
                                </Grid>
                                <Grid item xs={12} md={6}>
                                    <Typography variant="h6" sx={{ mb: 2, fontWeight: 600 }}>
                                        Active Alerts
                                    </Typography>
                                    <List>
                                        {selectedEquipment.alerts
                                            .filter(alert => !alert.resolved)
                                            .slice(0, 5)
                                            .map((alert) => (
                                                <ListItem key={alert.id} sx={{ px: 0 }}>
                                                    <ListItemIcon>
                                                        {getAlertIcon(alert.type)}
                                                    </ListItemIcon>
                                                    <ListItemText
                                                        primary={alert.title}
                                                        secondary={alert.message}
                                                    />
                                                </ListItem>
                                            ))}
                                        {selectedEquipment.alerts.filter(a => !a.resolved).length === 0 && (
                                            <ListItem sx={{ px: 0 }}>
                                                <ListItemIcon>
                                                    <CheckCircle sx={{ color: hvacColors.success.main }} />
                                                </ListItemIcon>
                                                <ListItemText
                                                    primary="No Active Alerts"
                                                    secondary="Equipment is operating normally"
                                                />
                                            </ListItem>
                                        )}
                                    </List>
                                </Grid>
                            </Grid>
                        </DialogContent>
                        <DialogActions sx={{ p: spacing.md, gap: 1 }}>
                            <Button
                                startIcon={<Edit />}
                                onClick={() => {
                                    onEditEquipment?.(selectedEquipment.id);
                                    setDetailsOpen(false);
                                }}
                            >
                                Edit
                            </Button>
                            <Button
                                startIcon={<Schedule />}
                                onClick={() => {
                                    onScheduleMaintenance?.(selectedEquipment.id);
                                    setDetailsOpen(false);
                                }}
                            >
                                Schedule Maintenance
                            </Button>
                            <Button
                                startIcon={<Assignment />}
                                onClick={() => {
                                    onCreateOrder?.(selectedEquipment.id);
                                    setDetailsOpen(false);
                                }}
                                variant="contained"
                                sx={{
                                    background: `linear-gradient(135deg, ${hvacColors.primary.main} 0%, ${hvacColors.secondary.main} 100%)`,
                                }}
                            >
                                Create Service Order
                            </Button>
                        </DialogActions>
                    </>
                )}
            </Dialog>
        </Box>
    );
};

export default CosmicEquipmentRegistry;
