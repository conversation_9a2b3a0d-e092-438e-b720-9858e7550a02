import React from 'react';
import {
    <PERSON><PERSON>,
    <PERSON>conButton,
    <PERSON>ab,
    ButtonGroup,
    CircularProgress,
    Box,
    useTheme,
    alpha,
} from '@mui/material';
import { styled, keyframes } from '@mui/material/styles';

// Cosmic animations
const ripple = keyframes`
    0% {
        transform: scale(0);
        opacity: 1;
    }
    100% {
        transform: scale(4);
        opacity: 0;
    }
`;

const glow = keyframes`
    0%, 100% {
        box-shadow: 0 0 5px currentColor;
    }
    50% {
        box-shadow: 0 0 20px currentColor, 0 0 30px currentColor;
    }
`;

const pulse = keyframes`
    0% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.05);
    }
    100% {
        transform: scale(1);
    }
`;

const shimmer = keyframes`
    0% {
        background-position: -200px 0;
    }
    100% {
        background-position: calc(200px + 100%) 0;
    }
`;

// Styled Components
const StyledButton = styled(But<PERSON>, {
    shouldForwardProp: (prop) => !['variant', 'cosmic', 'glowing', 'pulsing'].includes(prop as string),
})<{
    variant?: 'cosmic' | 'gradient' | 'glass' | 'neon';
    cosmic?: boolean;
    glowing?: boolean;
    pulsing?: boolean;
}>(({ theme, variant = 'cosmic', cosmic = false, glowing = false, pulsing = false }) => ({
    position: 'relative',
    overflow: 'hidden',
    borderRadius: 12,
    fontWeight: 600,
    textTransform: 'none',
    transition: 'all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94)',
    
    // Base cosmic styling
    ...(cosmic && {
        background: `linear-gradient(45deg, ${theme.palette.primary.main} 30%, ${theme.palette.secondary.main} 90%)`,
        color: theme.palette.primary.contrastText,
        boxShadow: '0 4px 15px rgba(0, 0, 0, 0.2)',
        
        '&:hover': {
            transform: 'translateY(-2px)',
            boxShadow: '0 8px 25px rgba(0, 0, 0, 0.3)',
        },
        
        '&:active': {
            transform: 'translateY(0)',
        },
    }),
    
    // Variant styles
    ...(variant === 'gradient' && {
        background: `linear-gradient(135deg, ${theme.palette.primary.main} 0%, ${theme.palette.secondary.main} 100%)`,
        color: theme.palette.primary.contrastText,
        border: 'none',
        
        '&:hover': {
            background: `linear-gradient(135deg, ${theme.palette.primary.dark} 0%, ${theme.palette.secondary.dark} 100%)`,
        },
    }),
    
    ...(variant === 'glass' && {
        background: alpha(theme.palette.background.paper, 0.1),
        backdropFilter: 'blur(10px)',
        border: `1px solid ${alpha(theme.palette.primary.main, 0.2)}`,
        color: theme.palette.primary.main,
        
        '&:hover': {
            background: alpha(theme.palette.primary.main, 0.1),
            borderColor: theme.palette.primary.main,
        },
    }),
    
    ...(variant === 'neon' && {
        background: 'transparent',
        border: `2px solid ${theme.palette.primary.main}`,
        color: theme.palette.primary.main,
        boxShadow: `0 0 10px ${alpha(theme.palette.primary.main, 0.3)}`,
        
        '&:hover': {
            background: alpha(theme.palette.primary.main, 0.1),
            boxShadow: `0 0 20px ${alpha(theme.palette.primary.main, 0.6)}`,
        },
    }),
    
    // Animation effects
    ...(glowing && {
        animation: `${glow} 2s ease-in-out infinite`,
    }),
    
    ...(pulsing && {
        animation: `${pulse} 2s ease-in-out infinite`,
    }),
    
    // Ripple effect
    '&::before': {
        content: '""',
        position: 'absolute',
        top: '50%',
        left: '50%',
        width: 0,
        height: 0,
        borderRadius: '50%',
        background: alpha(theme.palette.common.white, 0.3),
        transform: 'translate(-50%, -50%)',
        transition: 'width 0.6s, height 0.6s',
    },
    
    '&:active::before': {
        width: '300px',
        height: '300px',
        animation: `${ripple} 0.6s ease-out`,
    },
}));

const StyledIconButton = styled(IconButton, {
    shouldForwardProp: (prop) => !['variant', 'cosmic'].includes(prop as string),
})<{
    variant?: 'cosmic' | 'gradient' | 'glass' | 'neon';
    cosmic?: boolean;
}>(({ theme, variant = 'cosmic', cosmic = false }) => ({
    transition: 'all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94)',
    
    ...(cosmic && {
        background: `linear-gradient(45deg, ${theme.palette.primary.main} 30%, ${theme.palette.secondary.main} 90%)`,
        color: theme.palette.primary.contrastText,
        
        '&:hover': {
            transform: 'scale(1.1) rotate(5deg)',
            boxShadow: '0 8px 25px rgba(0, 0, 0, 0.3)',
        },
    }),
    
    ...(variant === 'glass' && {
        background: alpha(theme.palette.background.paper, 0.1),
        backdropFilter: 'blur(10px)',
        border: `1px solid ${alpha(theme.palette.primary.main, 0.2)}`,
        
        '&:hover': {
            background: alpha(theme.palette.primary.main, 0.1),
        },
    }),
}));

const StyledFab = styled(Fab, {
    shouldForwardProp: (prop) => !['variant', 'cosmic'].includes(prop as string),
})<{
    variant?: 'cosmic' | 'gradient' | 'glass';
    cosmic?: boolean;
}>(({ theme, variant = 'cosmic', cosmic = false }) => ({
    transition: 'all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94)',
    
    ...(cosmic && {
        background: `linear-gradient(45deg, ${theme.palette.primary.main} 30%, ${theme.palette.secondary.main} 90%)`,
        
        '&:hover': {
            transform: 'scale(1.1)',
            boxShadow: '0 12px 40px rgba(0, 0, 0, 0.3)',
        },
    }),
}));

// Component Props
interface CosmicButtonProps {
    children: React.ReactNode;
    variant?: 'cosmic' | 'gradient' | 'glass' | 'neon';
    size?: 'small' | 'medium' | 'large';
    color?: 'primary' | 'secondary' | 'success' | 'warning' | 'error';
    cosmic?: boolean;
    glowing?: boolean;
    pulsing?: boolean;
    loading?: boolean;
    disabled?: boolean;
    fullWidth?: boolean;
    startIcon?: React.ReactNode;
    endIcon?: React.ReactNode;
    onClick?: () => void;
    type?: 'button' | 'submit' | 'reset';
    className?: string;
    sx?: any;
}

export const CosmicButton: React.FC<CosmicButtonProps> = ({
    children,
    variant = 'cosmic',
    size = 'medium',
    color = 'primary',
    cosmic = false,
    glowing = false,
    pulsing = false,
    loading = false,
    disabled = false,
    fullWidth = false,
    startIcon,
    endIcon,
    onClick,
    type = 'button',
    className,
    sx = {},
}) => {
    return (
        <StyledButton
            variant={variant}
            size={size}
            color={color}
            cosmic={cosmic}
            glowing={glowing}
            pulsing={pulsing}
            disabled={disabled || loading}
            fullWidth={fullWidth}
            startIcon={loading ? <CircularProgress size={16} color="inherit" /> : startIcon}
            endIcon={!loading ? endIcon : undefined}
            onClick={onClick}
            type={type}
            className={className}
            sx={sx}
        >
            {children}
        </StyledButton>
    );
};

// Specialized Button Components
export const CosmicIconButton: React.FC<{
    children: React.ReactNode;
    variant?: 'cosmic' | 'gradient' | 'glass' | 'neon';
    size?: 'small' | 'medium' | 'large';
    cosmic?: boolean;
    onClick?: () => void;
    disabled?: boolean;
    className?: string;
    sx?: any;
}> = ({
    children,
    variant = 'cosmic',
    size = 'medium',
    cosmic = false,
    onClick,
    disabled = false,
    className,
    sx = {},
}) => {
    return (
        <StyledIconButton
            variant={variant}
            size={size}
            cosmic={cosmic}
            onClick={onClick}
            disabled={disabled}
            className={className}
            sx={sx}
        >
            {children}
        </StyledIconButton>
    );
};

export const CosmicFab: React.FC<{
    children: React.ReactNode;
    variant?: 'cosmic' | 'gradient' | 'glass';
    size?: 'small' | 'medium' | 'large';
    cosmic?: boolean;
    onClick?: () => void;
    disabled?: boolean;
    className?: string;
    sx?: any;
}> = ({
    children,
    variant = 'cosmic',
    size = 'large',
    cosmic = false,
    onClick,
    disabled = false,
    className,
    sx = {},
}) => {
    return (
        <StyledFab
            variant={variant}
            size={size}
            cosmic={cosmic}
            onClick={onClick}
            disabled={disabled}
            className={className}
            sx={sx}
        >
            {children}
        </StyledFab>
    );
};

// Button Group Component
export const CosmicButtonGroup: React.FC<{
    children: React.ReactNode;
    variant?: 'text' | 'outlined' | 'contained';
    orientation?: 'horizontal' | 'vertical';
    size?: 'small' | 'medium' | 'large';
    className?: string;
    sx?: any;
}> = ({
    children,
    variant = 'contained',
    orientation = 'horizontal',
    size = 'medium',
    className,
    sx = {},
}) => {
    const theme = useTheme();
    
    return (
        <ButtonGroup
            variant={variant}
            orientation={orientation}
            size={size}
            className={className}
            sx={{
                '& .MuiButton-root': {
                    borderRadius: orientation === 'horizontal' ? '12px 0 0 12px' : '12px 12px 0 0',
                    transition: 'all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94)',
                    
                    '&:hover': {
                        transform: 'translateY(-2px)',
                        zIndex: 1,
                    },
                    
                    '&:last-child': {
                        borderRadius: orientation === 'horizontal' ? '0 12px 12px 0' : '0 0 12px 12px',
                    },
                    
                    '&:not(:first-of-type):not(:last-child)': {
                        borderRadius: 0,
                    },
                },
                ...sx,
            }}
        >
            {children}
        </ButtonGroup>
    );
};
