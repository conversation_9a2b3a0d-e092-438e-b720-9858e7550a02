import React, { useState, useCallback } from 'react';
import {
    Box,
    Paper,
    Typography,
    Card,
    CardContent,
    Avatar,
    Chip,
    IconButton,
    Badge,
    Tooltip,
    LinearProgress,
    useTheme,
    alpha,
} from '@mui/material';
import {
    DragDropContext,
    Droppable,
    Draggable,
    DropResult,
} from '@hello-pangea/dnd';
import {
    MoreVert,
    Schedule,
    Person,
    LocationOn,
    Priority,
    Build,
    Warning,
    CheckCircle,
    AccessTime,
    AttachMoney,
} from '@mui/icons-material';
import { hvacColors, spacing, animations, PHI } from '../../theme/CosmicTheme';

interface KanbanCard {
    id: string;
    title: string;
    description?: string;
    assignee?: {
        id: string;
        name: string;
        avatar?: string;
    };
    customer?: {
        id: string;
        name: string;
        location?: string;
    };
    priority: 'low' | 'medium' | 'high' | 'urgent';
    dueDate?: Date;
    estimatedHours?: number;
    actualHours?: number;
    progress?: number;
    tags?: string[];
    equipment?: string[];
    value?: number;
    type: 'installation' | 'maintenance' | 'repair' | 'inspection';
}

interface KanbanColumn {
    id: string;
    title: string;
    cards: KanbanCard[];
    color: string;
    limit?: number;
    description?: string;
}

interface CosmicKanbanBoardProps {
    columns: KanbanColumn[];
    onCardMove: (cardId: string, sourceColumnId: string, destinationColumnId: string, newIndex: number) => void;
    onCardClick?: (card: KanbanCard) => void;
    onCardAction?: (action: string, card: KanbanCard) => void;
    onColumnAction?: (action: string, column: KanbanColumn) => void;
    showMetrics?: boolean;
    compactMode?: boolean;
}

const CosmicKanbanBoard: React.FC<CosmicKanbanBoardProps> = ({
    columns,
    onCardMove,
    onCardClick,
    onCardAction,
    onColumnAction,
    showMetrics = true,
    compactMode = false,
}) => {
    const theme = useTheme();
    const [draggedCard, setDraggedCard] = useState<string | null>(null);

    const getPriorityColor = (priority: string) => {
        switch (priority) {
            case 'urgent': return hvacColors.error.main;
            case 'high': return hvacColors.warning.main;
            case 'medium': return hvacColors.info.main;
            case 'low': return hvacColors.success.main;
            default: return theme.palette.text.secondary;
        }
    };

    const getTypeIcon = (type: string) => {
        switch (type) {
            case 'installation': return <Build />;
            case 'maintenance': return <Schedule />;
            case 'repair': return <Warning />;
            case 'inspection': return <CheckCircle />;
            default: return <Build />;
        }
    };

    const getTypeColor = (type: string) => {
        switch (type) {
            case 'installation': return hvacColors.primary.main;
            case 'maintenance': return hvacColors.secondary.main;
            case 'repair': return hvacColors.error.main;
            case 'inspection': return hvacColors.success.main;
            default: return theme.palette.text.secondary;
        }
    };

    const handleDragStart = useCallback((start: any) => {
        setDraggedCard(start.draggableId);
    }, []);

    const handleDragEnd = useCallback((result: DropResult) => {
        setDraggedCard(null);
        
        if (!result.destination) return;

        const { draggableId, source, destination } = result;
        
        if (source.droppableId === destination.droppableId && source.index === destination.index) {
            return;
        }

        onCardMove(draggableId, source.droppableId, destination.droppableId, destination.index);
    }, [onCardMove]);

    const renderCard = (card: KanbanCard, index: number) => (
        <Draggable key={card.id} draggableId={card.id} index={index}>
            {(provided, snapshot) => (
                <Card
                    ref={provided.innerRef}
                    {...provided.draggableProps}
                    {...provided.dragHandleProps}
                    onClick={() => onCardClick?.(card)}
                    sx={{
                        mb: spacing.sm,
                        cursor: 'pointer',
                        transition: `all ${animations.duration.standard}ms ${animations.easing.cosmic}`,
                        transform: snapshot.isDragging ? 'rotate(5deg)' : 'none',
                        boxShadow: snapshot.isDragging 
                            ? `0 12px 40px ${alpha(theme.palette.common.black, 0.15)}`
                            : `0 2px 8px ${alpha(theme.palette.common.black, 0.08)}`,
                        borderLeft: `4px solid ${getPriorityColor(card.priority)}`,
                        '&:hover': {
                            transform: snapshot.isDragging ? 'rotate(5deg)' : 'translateY(-2px)',
                            boxShadow: `0 8px 32px ${alpha(theme.palette.common.black, 0.12)}`,
                        },
                    }}
                >
                    <CardContent sx={{ p: spacing.sm, '&:last-child': { pb: spacing.sm } }}>
                        {/* Header */}
                        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start', mb: 1 }}>
                            <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, flex: 1 }}>
                                <Box sx={{ color: getTypeColor(card.type) }}>
                                    {getTypeIcon(card.type)}
                                </Box>
                                <Typography variant="subtitle2" sx={{ fontWeight: 600, flex: 1 }}>
                                    {card.title}
                                </Typography>
                            </Box>
                            <IconButton 
                                size="small" 
                                onClick={(e) => {
                                    e.stopPropagation();
                                    onCardAction?.('menu', card);
                                }}
                                sx={{ ml: 1 }}
                            >
                                <MoreVert fontSize="small" />
                            </IconButton>
                        </Box>

                        {/* Description */}
                        {card.description && !compactMode && (
                            <Typography variant="body2" color="text.secondary" sx={{ mb: 1, fontSize: '0.75rem' }}>
                                {card.description.length > 80 ? `${card.description.substring(0, 80)}...` : card.description}
                            </Typography>
                        )}

                        {/* Customer & Location */}
                        {card.customer && (
                            <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 1 }}>
                                <Person sx={{ fontSize: 14, color: theme.palette.text.secondary }} />
                                <Typography variant="caption" color="text.secondary">
                                    {card.customer.name}
                                </Typography>
                                {card.customer.location && (
                                    <>
                                        <LocationOn sx={{ fontSize: 12, color: theme.palette.text.secondary }} />
                                        <Typography variant="caption" color="text.secondary">
                                            {card.customer.location}
                                        </Typography>
                                    </>
                                )}
                            </Box>
                        )}

                        {/* Progress */}
                        {card.progress !== undefined && (
                            <Box sx={{ mb: 1 }}>
                                <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 0.5 }}>
                                    <Typography variant="caption" color="text.secondary">
                                        Progress
                                    </Typography>
                                    <Typography variant="caption" sx={{ fontWeight: 600 }}>
                                        {card.progress}%
                                    </Typography>
                                </Box>
                                <LinearProgress
                                    variant="determinate"
                                    value={card.progress}
                                    sx={{
                                        height: 4,
                                        borderRadius: 2,
                                        backgroundColor: alpha(theme.palette.primary.main, 0.1),
                                        '& .MuiLinearProgress-bar': {
                                            borderRadius: 2,
                                            background: `linear-gradient(90deg, ${hvacColors.primary.main} 0%, ${hvacColors.secondary.main} 100%)`,
                                        },
                                    }}
                                />
                            </Box>
                        )}

                        {/* Tags */}
                        {card.tags && card.tags.length > 0 && (
                            <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 0.5, mb: 1 }}>
                                {card.tags.slice(0, 3).map((tag) => (
                                    <Chip
                                        key={tag}
                                        label={tag}
                                        size="small"
                                        sx={{
                                            height: 20,
                                            fontSize: '0.65rem',
                                            backgroundColor: alpha(hvacColors.primary.main, 0.1),
                                            color: hvacColors.primary.main,
                                            '& .MuiChip-label': { px: 1 },
                                        }}
                                    />
                                ))}
                                {card.tags.length > 3 && (
                                    <Chip
                                        label={`+${card.tags.length - 3}`}
                                        size="small"
                                        sx={{
                                            height: 20,
                                            fontSize: '0.65rem',
                                            backgroundColor: alpha(theme.palette.text.secondary, 0.1),
                                            color: theme.palette.text.secondary,
                                            '& .MuiChip-label': { px: 1 },
                                        }}
                                    />
                                )}
                            </Box>
                        )}

                        {/* Footer */}
                        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mt: 1 }}>
                            {/* Assignee */}
                            {card.assignee && (
                                <Tooltip title={card.assignee.name}>
                                    <Avatar
                                        src={card.assignee.avatar}
                                        sx={{
                                            width: 24,
                                            height: 24,
                                            fontSize: '0.75rem',
                                            border: `2px solid ${alpha(hvacColors.primary.main, 0.2)}`,
                                        }}
                                    >
                                        {card.assignee.name.charAt(0)}
                                    </Avatar>
                                </Tooltip>
                            )}

                            {/* Time & Value */}
                            <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                                {card.estimatedHours && (
                                    <Tooltip title={`Estimated: ${card.estimatedHours}h`}>
                                        <Box sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}>
                                            <AccessTime sx={{ fontSize: 12, color: theme.palette.text.secondary }} />
                                            <Typography variant="caption" color="text.secondary">
                                                {card.estimatedHours}h
                                            </Typography>
                                        </Box>
                                    </Tooltip>
                                )}
                                {card.value && (
                                    <Tooltip title={`Value: $${card.value.toLocaleString()}`}>
                                        <Box sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}>
                                            <AttachMoney sx={{ fontSize: 12, color: hvacColors.success.main }} />
                                            <Typography variant="caption" sx={{ color: hvacColors.success.main, fontWeight: 600 }}>
                                                ${card.value.toLocaleString()}
                                            </Typography>
                                        </Box>
                                    </Tooltip>
                                )}
                            </Box>
                        </Box>

                        {/* Due Date */}
                        {card.dueDate && (
                            <Box sx={{ 
                                mt: 1, 
                                pt: 1, 
                                borderTop: `1px solid ${alpha(theme.palette.divider, 0.1)}`,
                                display: 'flex',
                                alignItems: 'center',
                                gap: 0.5,
                            }}>
                                <Schedule sx={{ fontSize: 12, color: theme.palette.text.secondary }} />
                                <Typography variant="caption" color="text.secondary">
                                    Due: {card.dueDate.toLocaleDateString()}
                                </Typography>
                            </Box>
                        )}
                    </CardContent>
                </Card>
            )}
        </Draggable>
    );

    const renderColumn = (column: KanbanColumn) => (
        <Paper
            key={column.id}
            sx={{
                width: compactMode ? 280 : 320,
                minHeight: 400,
                mr: spacing.md,
                borderRadius: 3,
                overflow: 'hidden',
                boxShadow: `0 4px 16px ${alpha(theme.palette.common.black, 0.08)}`,
                border: `1px solid ${alpha(column.color, 0.2)}`,
            }}
        >
            {/* Column Header */}
            <Box
                sx={{
                    p: spacing.md,
                    background: `linear-gradient(135deg, ${alpha(column.color, 0.1)} 0%, ${alpha(column.color, 0.05)} 100%)`,
                    borderBottom: `2px solid ${alpha(column.color, 0.2)}`,
                }}
            >
                <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 1 }}>
                    <Typography variant="h6" sx={{ fontWeight: 600, color: column.color }}>
                        {column.title}
                    </Typography>
                    <Badge
                        badgeContent={column.cards.length}
                        color="primary"
                        sx={{
                            '& .MuiBadge-badge': {
                                backgroundColor: column.color,
                                color: 'white',
                            },
                        }}
                    />
                </Box>
                {column.description && (
                    <Typography variant="caption" color="text.secondary">
                        {column.description}
                    </Typography>
                )}
                {column.limit && column.cards.length >= column.limit && (
                    <Chip
                        label="At Limit"
                        size="small"
                        color="warning"
                        sx={{ mt: 1 }}
                    />
                )}
            </Box>

            {/* Column Content */}
            <Droppable droppableId={column.id}>
                {(provided, snapshot) => (
                    <Box
                        ref={provided.innerRef}
                        {...provided.droppableProps}
                        sx={{
                            p: spacing.sm,
                            minHeight: 300,
                            backgroundColor: snapshot.isDraggingOver 
                                ? alpha(column.color, 0.05)
                                : 'transparent',
                            transition: `background-color ${animations.duration.short}ms ${animations.easing.cosmic}`,
                        }}
                    >
                        {column.cards.map((card, index) => renderCard(card, index))}
                        {provided.placeholder}
                    </Box>
                )}
            </Droppable>
        </Paper>
    );

    return (
        <DragDropContext onDragStart={handleDragStart} onDragEnd={handleDragEnd}>
            <Box
                sx={{
                    display: 'flex',
                    overflowX: 'auto',
                    pb: spacing.md,
                    minHeight: 500,
                    '&::-webkit-scrollbar': {
                        height: 8,
                    },
                    '&::-webkit-scrollbar-track': {
                        backgroundColor: alpha(theme.palette.divider, 0.1),
                        borderRadius: 4,
                    },
                    '&::-webkit-scrollbar-thumb': {
                        backgroundColor: alpha(hvacColors.primary.main, 0.3),
                        borderRadius: 4,
                        '&:hover': {
                            backgroundColor: alpha(hvacColors.primary.main, 0.5),
                        },
                    },
                }}
            >
                {columns.map(renderColumn)}
            </Box>
        </DragDropContext>
    );
};

export default CosmicKanbanBoard;
