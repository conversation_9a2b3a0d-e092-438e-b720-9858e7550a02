import React, { useState, useEffect } from 'react';
import {
    Box,
    Paper,
    Typography,
    Avatar,
    Chip,
    Tab,
    Tabs,
    Card,
    CardContent,
    Grid,
    List,
    ListItem,
    ListItemText,
    ListItemIcon,
    LinearProgress,
    IconButton,
    Button,
    Divider,
    Badge,
    Tooltip,
    useTheme,
    alpha,
} from '@mui/material';
import {
    Person,
    Email,
    Phone,
    LocationOn,
    Business,
    TrendingUp,
    TrendingDown,
    AttachMoney,
    Schedule,
    Build,
    Warning,
    CheckCircle,
    Star,
    StarBorder,
    Edit,
    Message,
    Call,
    Assignment,
    Receipt,
    Timeline,
    Psychology,
    Insights,
    History,
} from '@mui/icons-material';
import { hvacColors, spacing, animations, PHI } from '../../theme/CosmicTheme';

interface CustomerData {
    id: string;
    name: string;
    email: string;
    phone: string;
    address: string;
    company?: string;
    avatar?: string;
    type: 'residential' | 'commercial';
    status: 'active' | 'inactive' | 'prospect';
    healthScore: number;
    totalValue: number;
    lastContact: Date;
    nextAction?: string;
    tags: string[];
    rating: number;
    createdAt: Date;
}

interface CustomerMetrics {
    totalOrders: number;
    totalSpent: number;
    averageOrderValue: number;
    lastOrderDate?: Date;
    responseRate: number;
    satisfactionScore: number;
    churnRisk: 'low' | 'medium' | 'high';
    lifetimeValue: number;
}

interface Communication {
    id: string;
    type: 'email' | 'call' | 'meeting' | 'note';
    subject: string;
    content: string;
    timestamp: Date;
    direction: 'inbound' | 'outbound';
    sentiment?: 'positive' | 'neutral' | 'negative';
}

interface Equipment {
    id: string;
    type: string;
    brand: string;
    model: string;
    serialNumber: string;
    installDate: Date;
    warrantyExpiry?: Date;
    status: 'active' | 'maintenance' | 'repair' | 'inactive';
    healthScore: number;
    lastService?: Date;
    nextService?: Date;
}

interface CosmicCustomerProfileProps {
    customer: CustomerData;
    metrics: CustomerMetrics;
    communications: Communication[];
    equipment: Equipment[];
    onEdit?: () => void;
    onContact?: (type: 'email' | 'call') => void;
    onCreateOrder?: () => void;
    onScheduleService?: () => void;
}

const CosmicCustomerProfile: React.FC<CosmicCustomerProfileProps> = ({
    customer,
    metrics,
    communications,
    equipment,
    onEdit,
    onContact,
    onCreateOrder,
    onScheduleService,
}) => {
    const theme = useTheme();
    const [activeTab, setActiveTab] = useState(0);

    const getHealthScoreColor = (score: number) => {
        if (score >= 80) return hvacColors.success.main;
        if (score >= 60) return hvacColors.warning.main;
        return hvacColors.error.main;
    };

    const getChurnRiskColor = (risk: string) => {
        switch (risk) {
            case 'low': return hvacColors.success.main;
            case 'medium': return hvacColors.warning.main;
            case 'high': return hvacColors.error.main;
            default: return theme.palette.text.secondary;
        }
    };

    const getSentimentIcon = (sentiment?: string) => {
        switch (sentiment) {
            case 'positive': return <TrendingUp sx={{ color: hvacColors.success.main }} />;
            case 'negative': return <TrendingDown sx={{ color: hvacColors.error.main }} />;
            default: return <TrendingUp sx={{ color: theme.palette.text.secondary }} />;
        }
    };

    const renderStars = (rating: number) => {
        return Array.from({ length: 5 }, (_, index) => (
            <IconButton key={index} size="small" disabled>
                {index < rating ? (
                    <Star sx={{ color: hvacColors.warning.main, fontSize: 16 }} />
                ) : (
                    <StarBorder sx={{ color: theme.palette.text.disabled, fontSize: 16 }} />
                )}
            </IconButton>
        ));
    };

    const TabPanel = ({ children, value, index }: any) => (
        <div hidden={value !== index}>
            {value === index && <Box sx={{ py: spacing.md }}>{children}</Box>}
        </div>
    );

    return (
        <Paper 
            sx={{ 
                borderRadius: 3,
                overflow: 'hidden',
                boxShadow: `0 8px 32px ${alpha(theme.palette.common.black, 0.08)}`,
            }}
        >
            {/* Header */}
            <Box
                sx={{
                    p: spacing.lg,
                    background: `linear-gradient(135deg, ${alpha(hvacColors.primary.main, 0.1)} 0%, ${alpha(hvacColors.secondary.main, 0.05)} 100%)`,
                    borderBottom: `1px solid ${alpha(theme.palette.divider, 0.1)}`,
                }}
            >
                <Grid container spacing={spacing.md} alignItems="center">
                    <Grid item>
                        <Badge
                            overlap="circular"
                            anchorOrigin={{ vertical: 'bottom', horizontal: 'right' }}
                            badgeContent={
                                <Box
                                    sx={{
                                        width: 16,
                                        height: 16,
                                        borderRadius: '50%',
                                        backgroundColor: customer.status === 'active' ? hvacColors.success.main : hvacColors.error.main,
                                        border: `2px solid ${theme.palette.background.paper}`,
                                    }}
                                />
                            }
                        >
                            <Avatar
                                src={customer.avatar}
                                sx={{
                                    width: 80,
                                    height: 80,
                                    fontSize: '2rem',
                                    fontWeight: 600,
                                    border: `4px solid ${alpha(hvacColors.primary.main, 0.2)}`,
                                }}
                            >
                                {customer.name.charAt(0)}
                            </Avatar>
                        </Badge>
                    </Grid>
                    <Grid item xs>
                        <Typography variant="h4" sx={{ fontWeight: 700, mb: 1 }}>
                            {customer.name}
                        </Typography>
                        <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, mb: 2 }}>
                            <Chip
                                label={customer.type}
                                size="small"
                                sx={{
                                    backgroundColor: alpha(hvacColors.primary.main, 0.1),
                                    color: hvacColors.primary.main,
                                    fontWeight: 600,
                                }}
                            />
                            <Chip
                                label={customer.status}
                                size="small"
                                sx={{
                                    backgroundColor: alpha(getHealthScoreColor(customer.healthScore), 0.1),
                                    color: getHealthScoreColor(customer.healthScore),
                                    fontWeight: 600,
                                }}
                            />
                            <Box sx={{ display: 'flex', alignItems: 'center' }}>
                                {renderStars(customer.rating)}
                            </Box>
                        </Box>
                        <Grid container spacing={spacing.sm}>
                            <Grid item xs={12} sm={6}>
                                <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 1 }}>
                                    <Email sx={{ fontSize: 16, color: theme.palette.text.secondary }} />
                                    <Typography variant="body2">{customer.email}</Typography>
                                </Box>
                                <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                                    <Phone sx={{ fontSize: 16, color: theme.palette.text.secondary }} />
                                    <Typography variant="body2">{customer.phone}</Typography>
                                </Box>
                            </Grid>
                            <Grid item xs={12} sm={6}>
                                <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 1 }}>
                                    <LocationOn sx={{ fontSize: 16, color: theme.palette.text.secondary }} />
                                    <Typography variant="body2">{customer.address}</Typography>
                                </Box>
                                {customer.company && (
                                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                                        <Business sx={{ fontSize: 16, color: theme.palette.text.secondary }} />
                                        <Typography variant="body2">{customer.company}</Typography>
                                    </Box>
                                )}
                            </Grid>
                        </Grid>
                    </Grid>
                    <Grid item>
                        <Box sx={{ display: 'flex', flexDirection: 'column', gap: 1 }}>
                            <Button
                                variant="contained"
                                startIcon={<Edit />}
                                onClick={onEdit}
                                sx={{
                                    background: `linear-gradient(135deg, ${hvacColors.primary.main} 0%, ${hvacColors.secondary.main} 100%)`,
                                    '&:hover': {
                                        background: `linear-gradient(135deg, ${hvacColors.primary.dark} 0%, ${hvacColors.secondary.dark} 100%)`,
                                    },
                                }}
                            >
                                Edit Profile
                            </Button>
                            <Box sx={{ display: 'flex', gap: 1 }}>
                                <Tooltip title="Send Email">
                                    <IconButton
                                        onClick={() => onContact?.('email')}
                                        sx={{
                                            backgroundColor: alpha(hvacColors.primary.main, 0.1),
                                            '&:hover': {
                                                backgroundColor: alpha(hvacColors.primary.main, 0.2),
                                            },
                                        }}
                                    >
                                        <Message />
                                    </IconButton>
                                </Tooltip>
                                <Tooltip title="Make Call">
                                    <IconButton
                                        onClick={() => onContact?.('call')}
                                        sx={{
                                            backgroundColor: alpha(hvacColors.success.main, 0.1),
                                            '&:hover': {
                                                backgroundColor: alpha(hvacColors.success.main, 0.2),
                                            },
                                        }}
                                    >
                                        <Call />
                                    </IconButton>
                                </Tooltip>
                            </Box>
                        </Box>
                    </Grid>
                </Grid>

                {/* Health Score */}
                <Box sx={{ mt: spacing.md }}>
                    <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 1 }}>
                        <Typography variant="subtitle2" sx={{ fontWeight: 600 }}>
                            Customer Health Score
                        </Typography>
                        <Typography variant="h6" sx={{ fontWeight: 700, color: getHealthScoreColor(customer.healthScore) }}>
                            {customer.healthScore}/100
                        </Typography>
                    </Box>
                    <LinearProgress
                        variant="determinate"
                        value={customer.healthScore}
                        sx={{
                            height: 8,
                            borderRadius: 4,
                            backgroundColor: alpha(theme.palette.divider, 0.2),
                            '& .MuiLinearProgress-bar': {
                                borderRadius: 4,
                                backgroundColor: getHealthScoreColor(customer.healthScore),
                            },
                        }}
                    />
                </Box>
            </Box>

            {/* Metrics Cards */}
            <Box sx={{ p: spacing.md, borderBottom: `1px solid ${alpha(theme.palette.divider, 0.1)}` }}>
                <Grid container spacing={spacing.md}>
                    <Grid item xs={12} sm={6} md={3}>
                        <Card sx={{ textAlign: 'center', p: spacing.sm }}>
                            <Typography variant="h4" sx={{ fontWeight: 700, color: hvacColors.primary.main }}>
                                {metrics.totalOrders}
                            </Typography>
                            <Typography variant="caption" color="text.secondary">
                                Total Orders
                            </Typography>
                        </Card>
                    </Grid>
                    <Grid item xs={12} sm={6} md={3}>
                        <Card sx={{ textAlign: 'center', p: spacing.sm }}>
                            <Typography variant="h4" sx={{ fontWeight: 700, color: hvacColors.success.main }}>
                                ${metrics.totalSpent.toLocaleString()}
                            </Typography>
                            <Typography variant="caption" color="text.secondary">
                                Total Spent
                            </Typography>
                        </Card>
                    </Grid>
                    <Grid item xs={12} sm={6} md={3}>
                        <Card sx={{ textAlign: 'center', p: spacing.sm }}>
                            <Typography variant="h4" sx={{ fontWeight: 700, color: hvacColors.warning.main }}>
                                ${metrics.averageOrderValue.toLocaleString()}
                            </Typography>
                            <Typography variant="caption" color="text.secondary">
                                Avg Order Value
                            </Typography>
                        </Card>
                    </Grid>
                    <Grid item xs={12} sm={6} md={3}>
                        <Card sx={{ textAlign: 'center', p: spacing.sm }}>
                            <Typography variant="h4" sx={{ fontWeight: 700, color: getChurnRiskColor(metrics.churnRisk) }}>
                                {metrics.churnRisk.toUpperCase()}
                            </Typography>
                            <Typography variant="caption" color="text.secondary">
                                Churn Risk
                            </Typography>
                        </Card>
                    </Grid>
                </Grid>
            </Box>

            {/* Tabs */}
            <Box sx={{ borderBottom: 1, borderColor: 'divider' }}>
                <Tabs
                    value={activeTab}
                    onChange={(_, newValue) => setActiveTab(newValue)}
                    sx={{
                        '& .MuiTab-root': {
                            textTransform: 'none',
                            fontWeight: 600,
                            '&.Mui-selected': {
                                color: hvacColors.primary.main,
                            },
                        },
                        '& .MuiTabs-indicator': {
                            backgroundColor: hvacColors.primary.main,
                        },
                    }}
                >
                    <Tab icon={<Timeline />} label="Overview" />
                    <Tab icon={<Message />} label="Communications" />
                    <Tab icon={<Build />} label="Equipment" />
                    <Tab icon={<Receipt />} label="Financial" />
                    <Tab icon={<Psychology />} label="AI Insights" />
                </Tabs>
            </Box>

            {/* Tab Panels */}
            <Box sx={{ p: spacing.md }}>
                <TabPanel value={activeTab} index={0}>
                    <Grid container spacing={spacing.md}>
                        <Grid item xs={12} md={6}>
                            <Typography variant="h6" sx={{ mb: 2, fontWeight: 600 }}>
                                Recent Activity
                            </Typography>
                            <List>
                                {communications.slice(0, 5).map((comm) => (
                                    <ListItem key={comm.id} sx={{ px: 0 }}>
                                        <ListItemIcon>
                                            {getSentimentIcon(comm.sentiment)}
                                        </ListItemIcon>
                                        <ListItemText
                                            primary={comm.subject}
                                            secondary={`${comm.type} • ${comm.timestamp.toLocaleDateString()}`}
                                        />
                                    </ListItem>
                                ))}
                            </List>
                        </Grid>
                        <Grid item xs={12} md={6}>
                            <Typography variant="h6" sx={{ mb: 2, fontWeight: 600 }}>
                                Quick Actions
                            </Typography>
                            <Box sx={{ display: 'flex', flexDirection: 'column', gap: 1 }}>
                                <Button
                                    variant="outlined"
                                    startIcon={<Assignment />}
                                    onClick={onCreateOrder}
                                    fullWidth
                                >
                                    Create Service Order
                                </Button>
                                <Button
                                    variant="outlined"
                                    startIcon={<Schedule />}
                                    onClick={onScheduleService}
                                    fullWidth
                                >
                                    Schedule Maintenance
                                </Button>
                            </Box>
                        </Grid>
                    </Grid>
                </TabPanel>

                <TabPanel value={activeTab} index={1}>
                    <Typography variant="h6" sx={{ mb: 2, fontWeight: 600 }}>
                        Communication History
                    </Typography>
                    <List>
                        {communications.map((comm) => (
                            <ListItem key={comm.id} sx={{ border: `1px solid ${alpha(theme.palette.divider, 0.1)}`, borderRadius: 2, mb: 1 }}>
                                <ListItemIcon>
                                    {getSentimentIcon(comm.sentiment)}
                                </ListItemIcon>
                                <ListItemText
                                    primary={comm.subject}
                                    secondary={
                                        <Box>
                                            <Typography variant="body2" color="text.secondary">
                                                {comm.content.substring(0, 100)}...
                                            </Typography>
                                            <Typography variant="caption" color="text.secondary">
                                                {comm.type} • {comm.direction} • {comm.timestamp.toLocaleString()}
                                            </Typography>
                                        </Box>
                                    }
                                />
                            </ListItem>
                        ))}
                    </List>
                </TabPanel>

                <TabPanel value={activeTab} index={2}>
                    <Typography variant="h6" sx={{ mb: 2, fontWeight: 600 }}>
                        Equipment Registry
                    </Typography>
                    <Grid container spacing={spacing.md}>
                        {equipment.map((item) => (
                            <Grid item xs={12} md={6} key={item.id}>
                                <Card>
                                    <CardContent>
                                        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start', mb: 2 }}>
                                            <Typography variant="h6" sx={{ fontWeight: 600 }}>
                                                {item.brand} {item.model}
                                            </Typography>
                                            <Chip
                                                label={item.status}
                                                size="small"
                                                sx={{
                                                    backgroundColor: alpha(getHealthScoreColor(item.healthScore), 0.1),
                                                    color: getHealthScoreColor(item.healthScore),
                                                }}
                                            />
                                        </Box>
                                        <Typography variant="body2" color="text.secondary" sx={{ mb: 1 }}>
                                            {item.type} • SN: {item.serialNumber}
                                        </Typography>
                                        <Box sx={{ mb: 2 }}>
                                            <Typography variant="caption" color="text.secondary">
                                                Health Score: {item.healthScore}%
                                            </Typography>
                                            <LinearProgress
                                                variant="determinate"
                                                value={item.healthScore}
                                                sx={{
                                                    height: 4,
                                                    borderRadius: 2,
                                                    backgroundColor: alpha(theme.palette.divider, 0.2),
                                                    '& .MuiLinearProgress-bar': {
                                                        borderRadius: 2,
                                                        backgroundColor: getHealthScoreColor(item.healthScore),
                                                    },
                                                }}
                                            />
                                        </Box>
                                        <Typography variant="caption" color="text.secondary">
                                            Installed: {item.installDate.toLocaleDateString()}
                                            {item.nextService && ` • Next Service: ${item.nextService.toLocaleDateString()}`}
                                        </Typography>
                                    </CardContent>
                                </Card>
                            </Grid>
                        ))}
                    </Grid>
                </TabPanel>

                <TabPanel value={activeTab} index={3}>
                    <Typography variant="h6" sx={{ mb: 2, fontWeight: 600 }}>
                        Financial Overview
                    </Typography>
                    <Grid container spacing={spacing.md}>
                        <Grid item xs={12} md={4}>
                            <Card sx={{ textAlign: 'center', p: spacing.md }}>
                                <Typography variant="h4" sx={{ fontWeight: 700, color: hvacColors.success.main, mb: 1 }}>
                                    ${metrics.lifetimeValue.toLocaleString()}
                                </Typography>
                                <Typography variant="subtitle2" color="text.secondary">
                                    Lifetime Value
                                </Typography>
                            </Card>
                        </Grid>
                        <Grid item xs={12} md={4}>
                            <Card sx={{ textAlign: 'center', p: spacing.md }}>
                                <Typography variant="h4" sx={{ fontWeight: 700, color: hvacColors.primary.main, mb: 1 }}>
                                    {metrics.responseRate}%
                                </Typography>
                                <Typography variant="subtitle2" color="text.secondary">
                                    Response Rate
                                </Typography>
                            </Card>
                        </Grid>
                        <Grid item xs={12} md={4}>
                            <Card sx={{ textAlign: 'center', p: spacing.md }}>
                                <Typography variant="h4" sx={{ fontWeight: 700, color: hvacColors.warning.main, mb: 1 }}>
                                    {metrics.satisfactionScore}/5
                                </Typography>
                                <Typography variant="subtitle2" color="text.secondary">
                                    Satisfaction Score
                                </Typography>
                            </Card>
                        </Grid>
                    </Grid>
                </TabPanel>

                <TabPanel value={activeTab} index={4}>
                    <Typography variant="h6" sx={{ mb: 2, fontWeight: 600 }}>
                        AI-Powered Insights
                    </Typography>
                    <Grid container spacing={spacing.md}>
                        <Grid item xs={12} md={6}>
                            <Card>
                                <CardContent>
                                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 2 }}>
                                        <Insights sx={{ color: hvacColors.primary.main }} />
                                        <Typography variant="h6" sx={{ fontWeight: 600 }}>
                                            Recommendations
                                        </Typography>
                                    </Box>
                                    <List>
                                        <ListItem sx={{ px: 0 }}>
                                            <ListItemText
                                                primary="Schedule Preventive Maintenance"
                                                secondary="Based on equipment age and usage patterns"
                                            />
                                        </ListItem>
                                        <ListItem sx={{ px: 0 }}>
                                            <ListItemText
                                                primary="Upsell Opportunity"
                                                secondary="Customer shows interest in energy-efficient upgrades"
                                            />
                                        </ListItem>
                                    </List>
                                </CardContent>
                            </Card>
                        </Grid>
                        <Grid item xs={12} md={6}>
                            <Card>
                                <CardContent>
                                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 2 }}>
                                        <Psychology sx={{ color: hvacColors.secondary.main }} />
                                        <Typography variant="h6" sx={{ fontWeight: 600 }}>
                                            Behavioral Analysis
                                        </Typography>
                                    </Box>
                                    <Typography variant="body2" color="text.secondary">
                                        Customer typically responds to emails within 2 hours and prefers morning appointments. 
                                        High satisfaction with previous services indicates strong retention potential.
                                    </Typography>
                                </CardContent>
                            </Card>
                        </Grid>
                    </Grid>
                </TabPanel>
            </Box>
        </Paper>
    );
};

export default CosmicCustomerProfile;
