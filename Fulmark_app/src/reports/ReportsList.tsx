import { useState } from 'react';
import { List } from 'react-admin';
import {
    Card,
    CardContent,
    Typography,
    Box,
    Grid,
    Button,
    Paper,
    Chip,
    Avatar,
    IconButton,
    Dialog,
    DialogTitle,
    DialogContent,
    DialogActions,
} from '@mui/material';
import {
    <PERSON><PERSON><PERSON> as ReportsIcon,
    TrendingUp as TrendingUpIcon,
    <PERSON><PERSON>hart as PieChartIcon,
    Timeline as TimelineIcon,
    Assessment as AssessmentIcon,
    Download as DownloadIcon,
    Visibility as ViewIcon,
    DateRange as DateRangeIcon,
    AttachMoney as MoneyIcon,
    Build as ServiceIcon,
    People as PeopleIcon,
    Devices as DevicesIcon,
} from '@mui/icons-material';
import { 
    BarChart, 
    Bar, 
    XAxis, 
    YAxis, 
    CartesianGrid, 
    Tooltip, 
    Legend, 
    ResponsiveContainer,
    PieChart,
    Pie,
    Cell,
    LineChart,
    Line,
    Area,
    AreaChart,
} from 'recharts';

const ReportCard = ({ 
    title, 
    description, 
    icon, 
    color, 
    category,
    onView,
    onDownload,
}: {
    title: string;
    description: string;
    icon: React.ReactNode;
    color: string;
    category: string;
    onView?: () => void;
    onDownload?: () => void;
}) => (
    <Card sx={{ height: '100%' }}>
        <CardContent>
            <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                <Avatar sx={{ bgcolor: color, mr: 2 }}>
                    {icon}
                </Avatar>
                <Box sx={{ flexGrow: 1 }}>
                    <Typography variant="h6">{title}</Typography>
                    <Chip label={category} size="small" color="primary" variant="outlined" />
                </Box>
                <Box>
                    <IconButton onClick={onView} color="primary">
                        <ViewIcon />
                    </IconButton>
                    <IconButton onClick={onDownload} color="secondary">
                        <DownloadIcon />
                    </IconButton>
                </Box>
            </Box>
            <Typography variant="body2" color="textSecondary">
                {description}
            </Typography>
        </CardContent>
    </Card>
);

const SampleChart = ({ type, data }: { type: string; data: any[] }) => {
    const COLORS = ['#0088FE', '#00C49F', '#FFBB28', '#FF8042', '#8884D8'];

    switch (type) {
        case 'bar':
            return (
                <ResponsiveContainer width="100%" height={300}>
                    <BarChart data={data}>
                        <CartesianGrid strokeDasharray="3 3" />
                        <XAxis dataKey="name" />
                        <YAxis />
                        <Tooltip />
                        <Legend />
                        <Bar dataKey="value" fill="#8884d8" />
                    </BarChart>
                </ResponsiveContainer>
            );
        case 'pie':
            return (
                <ResponsiveContainer width="100%" height={300}>
                    <PieChart>
                        <Pie
                            data={data}
                            cx="50%"
                            cy="50%"
                            labelLine={false}
                            label={({ name, percent }) => `${name} ${(percent * 100).toFixed(0)}%`}
                            outerRadius={80}
                            fill="#8884d8"
                            dataKey="value"
                        >
                            {data.map((entry, index) => (
                                <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                            ))}
                        </Pie>
                        <Tooltip />
                    </PieChart>
                </ResponsiveContainer>
            );
        case 'line':
            return (
                <ResponsiveContainer width="100%" height={300}>
                    <LineChart data={data}>
                        <CartesianGrid strokeDasharray="3 3" />
                        <XAxis dataKey="name" />
                        <YAxis />
                        <Tooltip />
                        <Legend />
                        <Line type="monotone" dataKey="value" stroke="#8884d8" strokeWidth={2} />
                    </LineChart>
                </ResponsiveContainer>
            );
        case 'area':
            return (
                <ResponsiveContainer width="100%" height={300}>
                    <AreaChart data={data}>
                        <CartesianGrid strokeDasharray="3 3" />
                        <XAxis dataKey="name" />
                        <YAxis />
                        <Tooltip />
                        <Area type="monotone" dataKey="value" stroke="#8884d8" fill="#8884d8" />
                    </AreaChart>
                </ResponsiveContainer>
            );
        default:
            return <div>Nieznany typ wykresu</div>;
    }
};

export const ReportsList = () => {
    const [selectedReport, setSelectedReport] = useState<any>(null);
    const [dialogOpen, setDialogOpen] = useState(false);

    const reports = [
        {
            id: 'revenue_analysis',
            title: 'Analiza Przychodów',
            description: 'Szczegółowa analiza przychodów z podziałem na usługi, okresy i regiony.',
            icon: <MoneyIcon />,
            color: '#4caf50',
            category: 'Finanse',
            chartType: 'bar',
            data: [
                { name: 'Serwis', value: 45000 },
                { name: 'Instalacja', value: 62000 },
                { name: 'Konserwacja', value: 28000 },
                { name: 'Części', value: 15000 },
            ],
        },
        {
            id: 'service_performance',
            title: 'Wydajność Serwisu',
            description: 'Analiza wydajności techników, czasu realizacji zleceń i satysfakcji klientów.',
            icon: <ServiceIcon />,
            color: '#2196f3',
            category: 'Operacje',
            chartType: 'line',
            data: [
                { name: 'Sty', value: 85 },
                { name: 'Lut', value: 88 },
                { name: 'Mar', value: 92 },
                { name: 'Kwi', value: 89 },
                { name: 'Maj', value: 94 },
                { name: 'Cze', value: 96 },
            ],
        },
        {
            id: 'customer_analysis',
            title: 'Analiza Klientów',
            description: 'Segmentacja klientów, analiza retencji i wartości życiowej klienta.',
            icon: <PeopleIcon />,
            color: '#ff9800',
            category: 'Marketing',
            chartType: 'pie',
            data: [
                { name: 'Nowi', value: 35 },
                { name: 'Stali', value: 45 },
                { name: 'VIP', value: 15 },
                { name: 'Nieaktywni', value: 5 },
            ],
        },
        {
            id: 'equipment_status',
            title: 'Status Sprzętu',
            description: 'Raport o stanie sprzętu, harmonogramie konserwacji i przewidywanych awariach.',
            icon: <DevicesIcon />,
            color: '#9c27b0',
            category: 'Sprzęt',
            chartType: 'area',
            data: [
                { name: 'Sty', value: 95 },
                { name: 'Lut', value: 93 },
                { name: 'Mar', value: 97 },
                { name: 'Kwi', value: 94 },
                { name: 'Maj', value: 98 },
                { name: 'Cze', value: 96 },
            ],
        },
        {
            id: 'monthly_summary',
            title: 'Podsumowanie Miesięczne',
            description: 'Kompleksowy raport miesięczny z kluczowymi wskaźnikami biznesowymi.',
            icon: <AssessmentIcon />,
            color: '#607d8b',
            category: 'Zarządzanie',
            chartType: 'bar',
            data: [
                { name: 'Przychody', value: 150000 },
                { name: 'Koszty', value: 95000 },
                { name: 'Zysk', value: 55000 },
                { name: 'Marża %', value: 37 },
            ],
        },
        {
            id: 'trends_forecast',
            title: 'Trendy i Prognozy',
            description: 'Analiza trendów rynkowych i prognozy na najbliższe kwartały.',
            icon: <TrendingUpIcon />,
            color: '#f44336',
            category: 'Strategia',
            chartType: 'line',
            data: [
                { name: 'Q1', value: 120000 },
                { name: 'Q2', value: 135000 },
                { name: 'Q3', value: 148000 },
                { name: 'Q4', value: 162000 },
            ],
        },
    ];

    const handleViewReport = (report: any) => {
        setSelectedReport(report);
        setDialogOpen(true);
    };

    const handleDownloadReport = (report: any) => {
        // Simulate download
        console.log(`Pobieranie raportu: ${report.title}`);
        // Here you would implement actual download logic
    };

    const categories = ['Wszystkie', 'Finanse', 'Operacje', 'Marketing', 'Sprzęt', 'Zarządzanie', 'Strategia'];
    const [selectedCategory, setSelectedCategory] = useState('Wszystkie');

    const filteredReports = selectedCategory === 'Wszystkie' 
        ? reports 
        : reports.filter(report => report.category === selectedCategory);

    return (
        <List title="Raporty HVAC">
            <Box sx={{ p: 2 }}>
                <Grid container spacing={3}>
                    <Grid item xs={12}>
                        <Card sx={{ mb: 3, background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)' }}>
                            <CardContent>
                                <Box sx={{ display: 'flex', alignItems: 'center', color: 'white' }}>
                                    <ReportsIcon sx={{ fontSize: 48, mr: 2 }} />
                                    <Box>
                                        <Typography variant="h4" sx={{ color: 'white', mb: 1 }}>
                                            Centrum Raportów HVAC
                                        </Typography>
                                        <Typography variant="h6" sx={{ color: 'rgba(255,255,255,0.8)' }}>
                                            Kompleksowa analiza biznesowa i raportowanie
                                        </Typography>
                                    </Box>
                                </Box>
                            </CardContent>
                        </Card>
                    </Grid>

                    <Grid item xs={12}>
                        <Paper sx={{ p: 2, mb: 3 }}>
                            <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                                <DateRangeIcon sx={{ mr: 1 }} />
                                <Typography variant="h6">Filtry</Typography>
                            </Box>
                            <Box sx={{ display: 'flex', gap: 1, flexWrap: 'wrap' }}>
                                {categories.map((category) => (
                                    <Button
                                        key={category}
                                        variant={selectedCategory === category ? 'contained' : 'outlined'}
                                        onClick={() => setSelectedCategory(category)}
                                        size="small"
                                    >
                                        {category}
                                    </Button>
                                ))}
                            </Box>
                        </Paper>
                    </Grid>

                    {filteredReports.map((report) => (
                        <Grid item xs={12} md={6} lg={4} key={report.id}>
                            <ReportCard
                                title={report.title}
                                description={report.description}
                                icon={report.icon}
                                color={report.color}
                                category={report.category}
                                onView={() => handleViewReport(report)}
                                onDownload={() => handleDownloadReport(report)}
                            />
                        </Grid>
                    ))}
                </Grid>

                <Dialog open={dialogOpen} onClose={() => setDialogOpen(false)} maxWidth="lg" fullWidth>
                    <DialogTitle>
                        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                            {selectedReport?.icon}
                            {selectedReport?.title}
                        </Box>
                    </DialogTitle>
                    <DialogContent>
                        {selectedReport && (
                            <Box sx={{ p: 2 }}>
                                <Typography variant="body1" sx={{ mb: 3 }}>
                                    {selectedReport.description}
                                </Typography>
                                <SampleChart type={selectedReport.chartType} data={selectedReport.data} />
                            </Box>
                        )}
                    </DialogContent>
                    <DialogActions>
                        <Button onClick={() => handleDownloadReport(selectedReport)} startIcon={<DownloadIcon />}>
                            Pobierz PDF
                        </Button>
                        <Button onClick={() => setDialogOpen(false)}>
                            Zamknij
                        </Button>
                    </DialogActions>
                </Dialog>
            </Box>
        </List>
    );
};
