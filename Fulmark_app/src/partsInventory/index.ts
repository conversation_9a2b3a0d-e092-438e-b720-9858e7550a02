import PartsIcon from '@mui/icons-material/Inventory';

import { PartsInventoryList } from './PartsInventoryList';
import { PartsInventoryCreate } from './PartsInventoryCreate';
import { PartsInventoryEdit } from './PartsInventoryEdit';
import { PartsInventoryShow } from './PartsInventoryShow';

export default {
    list: PartsInventoryList,
    create: PartsInventoryCreate,
    edit: PartsInventoryEdit,
    show: PartsInventoryShow,
    icon: PartsIcon,
    recordRepresentation: (record: any) => `${record.part_number} - ${record.name}`,
};
