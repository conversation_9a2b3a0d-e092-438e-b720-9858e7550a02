import {
    List,
    Datagrid,
    TextField,
    NumberField,
    FunctionField,
    EditButton,
    ShowButton,
    DeleteButton,
    TopToolbar,
    CreateButton,
    ExportButton,
    FilterButton,
    SearchInput,
    SelectInput,
    NumberInput,
} from 'react-admin';
import { Chip, Box, LinearProgress, Typography } from '@mui/material';
import { CosmicButton } from '../components/cosmic/CosmicButton';

const partsFilters = [
    <SearchInput source="q" alwaysOn />,
    <SelectInput
        source="category"
        choices={[
            { id: 'filters', name: 'Filters' },
            { id: 'refrigerant', name: 'Refrigerant' },
            { id: 'electrical', name: 'Electrical' },
            { id: 'mechanical', name: 'Mechanical' },
            { id: 'sensors', name: 'Sensors' },
            { id: 'controls', name: 'Controls' },
            { id: 'other', name: 'Other' },
        ]}
    />,
    <SelectInput
        source="brand"
        choices={[
            { id: 'Daikin', name: 'Daikin' },
            { id: 'Mitsubishi', name: 'Mitsubishi' },
            { id: 'LG', name: 'L<PERSON>' },
            { id: 'Samsung', name: 'Samsung' },
            { id: 'Carrier', name: 'Carrier' },
            { id: 'Universal', name: 'Universal' },
        ]}
    />,
    <NumberInput source="quantity_in_stock_lte" label="Stock Below" />,
];

const PartsListActions = () => (
    <TopToolbar>
        <FilterButton />
        <CosmicButton cosmic startIcon={<span>+</span>}>
            Add Part
        </CosmicButton>
        <ExportButton />
    </TopToolbar>
);

const StockLevelField = ({ record }: { record?: any }) => {
    if (!record) return null;
    
    const { quantity_in_stock, minimum_stock_level } = record;
    const stockPercentage = minimum_stock_level > 0 
        ? (quantity_in_stock / minimum_stock_level) * 100 
        : 100;
    
    let color = '#4caf50'; // green for good stock
    let status = 'Good Stock';
    
    if (quantity_in_stock === 0) {
        color = '#f44336'; // red for out of stock
        status = 'Out of Stock';
    } else if (quantity_in_stock <= minimum_stock_level) {
        color = '#ff9800'; // orange for low stock
        status = 'Low Stock';
    }
    
    return (
        <Box sx={{ minWidth: 120 }}>
            <Box display="flex" alignItems="center" justifyContent="space-between" mb={0.5}>
                <Typography variant="body2" fontWeight={600}>
                    {quantity_in_stock}
                </Typography>
                <Chip
                    label={status}
                    size="small"
                    sx={{
                        backgroundColor: color,
                        color: 'white',
                        fontWeight: 500,
                        fontSize: '0.7rem',
                        height: 20,
                        ...(quantity_in_stock === 0 && {
                            animation: 'pulse 2s ease-in-out infinite',
                            '@keyframes pulse': {
                                '0%, 100%': { opacity: 1 },
                                '50%': { opacity: 0.7 },
                            },
                        }),
                    }}
                />
            </Box>
            <LinearProgress
                variant="determinate"
                value={Math.min(stockPercentage, 100)}
                sx={{
                    height: 6,
                    borderRadius: 3,
                    backgroundColor: 'rgba(0, 0, 0, 0.1)',
                    '& .MuiLinearProgress-bar': {
                        backgroundColor: color,
                        borderRadius: 3,
                    },
                }}
            />
            <Typography variant="caption" color="text.secondary">
                Min: {minimum_stock_level}
            </Typography>
        </Box>
    );
};

const CategoryField = ({ record }: { record?: any }) => {
    if (!record) return null;
    
    const categoryColors: Record<string, string> = {
        filters: '#2196f3',
        refrigerant: '#00bcd4',
        electrical: '#ff9800',
        mechanical: '#4caf50',
        sensors: '#9c27b0',
        controls: '#f44336',
        other: '#757575',
    };
    
    return (
        <Chip
            label={record.category}
            size="small"
            sx={{
                backgroundColor: categoryColors[record.category] || '#757575',
                color: 'white',
                fontWeight: 500,
                textTransform: 'capitalize',
            }}
        />
    );
};

const ValueField = ({ record }: { record?: any }) => {
    if (!record || !record.unit_cost || !record.quantity_in_stock) return null;
    
    const totalValue = record.unit_cost * record.quantity_in_stock;
    
    return (
        <Box>
            <Typography variant="body2" fontWeight={600}>
                {new Intl.NumberFormat('pl-PL', {
                    style: 'currency',
                    currency: 'PLN',
                }).format(totalValue)}
            </Typography>
            <Typography variant="caption" color="text.secondary">
                @ {new Intl.NumberFormat('pl-PL', {
                    style: 'currency',
                    currency: 'PLN',
                }).format(record.unit_cost)} each
            </Typography>
        </Box>
    );
};

export const PartsInventoryList = () => (
    <List
        filters={partsFilters}
        actions={<PartsListActions />}
        sort={{ field: 'quantity_in_stock', order: 'ASC' }}
        perPage={25}
    >
        <Datagrid rowClick="show" bulkActionButtons={false}>
            <TextField source="part_number" />
            <TextField source="name" />
            <TextField source="brand" />
            <FunctionField source="category" render={CategoryField} />
            <FunctionField source="quantity_in_stock" render={StockLevelField} />
            <FunctionField source="unit_cost" render={ValueField} />
            <TextField source="supplier" />
            <TextField source="location" />
            <ShowButton />
            <EditButton />
            <DeleteButton />
        </Datagrid>
    </List>
);
