import {
    List,
    Datagrid,
    TextField,
    DateField,
    ReferenceField,
    SelectField,
    FunctionField,
    EditButton,
    ShowButton,
    DeleteButton,
    TopToolbar,
    CreateButton,
    ExportButton,
    FilterButton,
    SearchInput,
    SelectInput,
    DateInput,
    ReferenceInput,
    AutocompleteInput,
} from 'react-admin';
import { Chip } from '@mui/material';

const equipmentFilters = [
    <SearchInput source="q" alwaysOn />,
    <SelectInput
        source="equipment_type"
        choices={[
            { id: 'air_conditioner', name: 'Air Conditioner' },
            { id: 'heat_pump', name: 'Heat Pump' },
            { id: 'ventilation', name: 'Ventilation' },
            { id: 'other', name: 'Other' },
        ]}
    />,
    <SelectInput
        source="status"
        choices={[
            { id: 'active', name: 'Active' },
            { id: 'inactive', name: 'Inactive' },
            { id: 'needs_service', name: 'Needs Service' },
            { id: 'replaced', name: 'Replaced' },
        ]}
    />,
    <SelectInput
        source="brand"
        choices={[
            { id: '<PERSON><PERSON>', name: '<PERSON><PERSON>' },
            { id: 'Mitsubishi', name: 'Mitsubishi' },
            { id: 'LG', name: 'L<PERSON>' },
            { id: 'Samsung', name: 'Samsung' },
            { id: 'Carrier', name: 'Carrier' },
            { id: 'Toshiba', name: 'Toshiba' },
        ]}
    />,
    <ReferenceInput source="company_id" reference="companies">
        <AutocompleteInput optionText="name" />
    </ReferenceInput>,
    <DateInput source="installation_date_gte" label="Installed After" />,
    <DateInput source="warranty_expiry_lte" label="Warranty Expires Before" />,
];

const EquipmentListActions = () => (
    <TopToolbar>
        <FilterButton />
        <CreateButton />
        <ExportButton />
    </TopToolbar>
);

const StatusField = ({ record }: { record?: any }) => {
    if (!record) return null;
    
    const statusColors: Record<string, string> = {
        active: '#4caf50',
        inactive: '#757575',
        needs_service: '#f44336',
        replaced: '#ff9800',
    };

    return (
        <Chip
            label={record.status}
            style={{
                backgroundColor: statusColors[record.status] || '#757575',
                color: 'white',
                textTransform: 'capitalize',
            }}
            size="small"
        />
    );
};

const WarrantyStatusField = ({ record }: { record?: any }) => {
    if (!record || !record.warranty_expiry) return null;
    
    const today = new Date();
    const warrantyDate = new Date(record.warranty_expiry);
    const isExpired = warrantyDate < today;
    const daysUntilExpiry = Math.ceil((warrantyDate.getTime() - today.getTime()) / (1000 * 60 * 60 * 24));
    
    let color = '#4caf50'; // green for valid warranty
    let label = 'Valid';
    
    if (isExpired) {
        color = '#f44336'; // red for expired
        label = 'Expired';
    } else if (daysUntilExpiry <= 30) {
        color = '#ff9800'; // orange for expiring soon
        label = 'Expiring Soon';
    }

    return (
        <Chip
            label={label}
            style={{
                backgroundColor: color,
                color: 'white',
            }}
            size="small"
        />
    );
};

export const EquipmentList = () => (
    <List
        filters={equipmentFilters}
        actions={<EquipmentListActions />}
        sort={{ field: 'created_at', order: 'DESC' }}
        perPage={25}
    >
        <Datagrid rowClick="show" bulkActionButtons={false}>
            <TextField source="brand" />
            <TextField source="model" />
            <TextField source="serial_number" />
            <SelectField
                source="equipment_type"
                choices={[
                    { id: 'air_conditioner', name: 'Air Conditioner' },
                    { id: 'heat_pump', name: 'Heat Pump' },
                    { id: 'ventilation', name: 'Ventilation' },
                    { id: 'other', name: 'Other' },
                ]}
            />
            <ReferenceField source="company_id" reference="companies" link="show">
                <TextField source="name" />
            </ReferenceField>
            <ReferenceField source="contact_id" reference="contacts" link="show">
                <FunctionField
                    render={(record: any) => 
                        record ? `${record.first_name} ${record.last_name}` : 'No contact'
                    }
                />
            </ReferenceField>
            <TextField source="location" />
            <TextField source="capacity" />
            <TextField source="refrigerant_type" />
            <FunctionField source="status" render={StatusField} />
            <DateField source="installation_date" />
            <DateField source="warranty_expiry" />
            <FunctionField source="warranty_expiry" render={WarrantyStatusField} />
            <ShowButton />
            <EditButton />
            <DeleteButton />
        </Datagrid>
    </List>
);
