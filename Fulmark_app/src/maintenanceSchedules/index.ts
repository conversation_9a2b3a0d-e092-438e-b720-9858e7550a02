import MaintenanceIcon from '@mui/icons-material/Schedule';

import { MaintenanceScheduleList } from './MaintenanceScheduleList';
import { MaintenanceScheduleCreate } from './MaintenanceScheduleCreate';
import { MaintenanceScheduleEdit } from './MaintenanceScheduleEdit';
import { MaintenanceScheduleShow } from './MaintenanceScheduleShow';

export default {
    list: MaintenanceScheduleList,
    create: MaintenanceScheduleCreate,
    edit: MaintenanceScheduleEdit,
    show: MaintenanceScheduleShow,
    icon: MaintenanceIcon,
    recordRepresentation: (record: any) => `${record.schedule_type} - ${record.equipment_info}`,
};
