import {
    List,
    Datagrid,
    TextField,
    DateField,
    ReferenceField,
    BooleanField,
    FunctionField,
    EditButton,
    ShowButton,
    DeleteButton,
    TopToolbar,
    CreateButton,
    ExportButton,
    FilterButton,
    SearchInput,
    SelectInput,
    DateInput,
    ReferenceInput,
    AutocompleteInput,
    BooleanInput,
} from 'react-admin';
import { Chip, Box, Typography } from '@mui/material';
import { CosmicButton } from '../components/cosmic/CosmicButton';

const maintenanceFilters = [
    <SearchInput source="q" alwaysOn />,
    <SelectInput
        source="schedule_type"
        choices={[
            { id: 'monthly', name: 'Monthly' },
            { id: 'quarterly', name: 'Quarterly' },
            { id: 'semi_annual', name: 'Semi-Annual' },
            { id: 'annual', name: 'Annual' },
        ]}
    />,
    <BooleanInput source="is_active" label="Active Only" defaultValue={true} />,
    <ReferenceInput source="equipment_id" reference="equipment">
        <AutocompleteInput
            optionText={(choice: any) => `${choice.brand} ${choice.model}`}
        />
    </ReferenceInput>,
    <ReferenceInput source="assigned_technician_id" reference="technicians">
        <AutocompleteInput
            optionText={(choice: any) => `${choice.first_name} ${choice.last_name}`}
        />
    </ReferenceInput>,
    <DateInput source="next_service_date_lte" label="Due Before" />,
];

const MaintenanceListActions = () => (
    <TopToolbar>
        <FilterButton />
        <CosmicButton cosmic startIcon={<span>+</span>}>
            New Schedule
        </CosmicButton>
        <ExportButton />
    </TopToolbar>
);

const ScheduleTypeField = ({ record }: { record?: any }) => {
    if (!record) return null;
    
    const typeColors: Record<string, string> = {
        monthly: '#4caf50',
        quarterly: '#2196f3',
        semi_annual: '#ff9800',
        annual: '#9c27b0',
    };
    
    return (
        <Chip
            label={record.schedule_type.replace('_', ' ')}
            size="small"
            sx={{
                backgroundColor: typeColors[record.schedule_type] || '#757575',
                color: 'white',
                fontWeight: 500,
                textTransform: 'capitalize',
            }}
        />
    );
};

const NextServiceField = ({ record }: { record?: any }) => {
    if (!record || !record.next_service_date) return null;
    
    const today = new Date();
    const nextService = new Date(record.next_service_date);
    const isOverdue = nextService < today;
    const daysDiff = Math.ceil((nextService.getTime() - today.getTime()) / (1000 * 60 * 60 * 24));
    
    let color = '#4caf50'; // green for future
    let status = 'Scheduled';
    let urgency = false;
    
    if (isOverdue) {
        color = '#f44336'; // red for overdue
        status = 'Overdue';
        urgency = true;
    } else if (daysDiff <= 7) {
        color = '#ff9800'; // orange for due soon
        status = 'Due Soon';
        urgency = true;
    }
    
    return (
        <Box>
            <DateField source="next_service_date" record={record} />
            <Chip
                label={status}
                size="small"
                sx={{
                    backgroundColor: color,
                    color: 'white',
                    fontWeight: 500,
                    fontSize: '0.7rem',
                    height: 20,
                    mt: 0.5,
                    ...(urgency && {
                        animation: 'pulse 2s ease-in-out infinite',
                        '@keyframes pulse': {
                            '0%, 100%': { opacity: 1 },
                            '50%': { opacity: 0.7 },
                        },
                    }),
                }}
            />
            {daysDiff > 0 && (
                <Typography variant="caption" color="text.secondary" display="block">
                    {daysDiff} days
                </Typography>
            )}
            {isOverdue && (
                <Typography variant="caption" color="error" display="block">
                    {Math.abs(daysDiff)} days overdue
                </Typography>
            )}
        </Box>
    );
};

const ActiveStatusField = ({ record }: { record?: any }) => {
    if (!record) return null;
    
    return (
        <Chip
            label={record.is_active ? 'Active' : 'Inactive'}
            size="small"
            variant={record.is_active ? 'filled' : 'outlined'}
            sx={{
                backgroundColor: record.is_active ? '#4caf50' : 'transparent',
                color: record.is_active ? 'white' : '#757575',
                borderColor: record.is_active ? '#4caf50' : '#757575',
                fontWeight: 500,
            }}
        />
    );
};

export const MaintenanceScheduleList = () => (
    <List
        filters={maintenanceFilters}
        actions={<MaintenanceListActions />}
        sort={{ field: 'next_service_date', order: 'ASC' }}
        perPage={25}
    >
        <Datagrid rowClick="show" bulkActionButtons={false}>
            <ReferenceField source="equipment_id" reference="equipment" link="show">
                <FunctionField
                    render={(record: any) => `${record.brand} ${record.model}`}
                />
            </ReferenceField>
            <FunctionField source="schedule_type" render={ScheduleTypeField} />
            <FunctionField source="next_service_date" render={NextServiceField} />
            <DateField source="last_service_date" />
            <ReferenceField source="assigned_technician_id" reference="technicians" link="show">
                <FunctionField
                    render={(record: any) => 
                        record ? `${record.first_name} ${record.last_name}` : 'Unassigned'
                    }
                />
            </ReferenceField>
            <TextField source="service_description" />
            <FunctionField source="is_active" render={ActiveStatusField} />
            <ShowButton />
            <EditButton />
            <DeleteButton />
        </Datagrid>
    </List>
);
