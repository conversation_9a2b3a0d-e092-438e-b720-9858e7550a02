import {
    List,
    Datagrid,
    TextField,
    DateField,
    ReferenceField,
    NumberField,
    FunctionField,
    EditButton,
    ShowButton,
    DeleteButton,
    TopToolbar,
    CreateButton,
    ExportButton,
    FilterButton,
    SearchInput,
    SelectInput,
    DateInput,
    ReferenceInput,
    AutocompleteInput,
} from 'react-admin';
import { Chip, Box } from '@mui/material';
import { CosmicButton } from '../components/cosmic/CosmicButton';
import { CosmicStatusIndicator } from '../components/cosmic/CosmicStatusIndicator';

const quoteFilters = [
    <SearchInput source="q" alwaysOn />,
    <SelectInput
        source="status"
        choices={[
            { id: 'draft', name: 'Draft' },
            { id: 'sent', name: '<PERSON><PERSON>' },
            { id: 'accepted', name: 'Accepted' },
            { id: 'rejected', name: 'Rejected' },
            { id: 'expired', name: 'Expired' },
        ]}
    />,
    <ReferenceInput source="company_id" reference="companies">
        <AutocompleteInput optionText="name" />
    </ReferenceInput>,
    <ReferenceInput source="technician_id" reference="technicians">
        <AutocompleteInput
            optionText={(choice: any) => `${choice.first_name} ${choice.last_name}`}
        />
    </ReferenceInput>,
    <DateInput source="valid_until_gte" label="Valid After" />,
    <DateInput source="valid_until_lte" label="Valid Before" />,
];

const QuoteListActions = () => (
    <TopToolbar>
        <FilterButton />
        <CosmicButton cosmic startIcon={<span>+</span>}>
            New Quote
        </CosmicButton>
        <ExportButton />
    </TopToolbar>
);

const QuoteStatusField = ({ record }: { record?: any }) => {
    if (!record) return null;
    
    const statusConfig = {
        draft: { color: '#757575', label: 'Draft' },
        sent: { color: '#2196f3', label: 'Sent' },
        accepted: { color: '#4caf50', label: 'Accepted' },
        rejected: { color: '#f44336', label: 'Rejected' },
        expired: { color: '#ff9800', label: 'Expired' },
    };
    
    const config = statusConfig[record.status as keyof typeof statusConfig];
    
    return (
        <Chip
            label={config.label}
            size="small"
            sx={{
                backgroundColor: config.color,
                color: 'white',
                fontWeight: 600,
            }}
        />
    );
};

const ValidityField = ({ record }: { record?: any }) => {
    if (!record || !record.valid_until) return null;
    
    const today = new Date();
    const validUntil = new Date(record.valid_until);
    const isExpired = validUntil < today;
    const daysUntilExpiry = Math.ceil((validUntil.getTime() - today.getTime()) / (1000 * 60 * 60 * 24));
    
    let color = '#4caf50'; // green for valid
    let label = `${daysUntilExpiry} days left`;
    
    if (isExpired) {
        color = '#f44336'; // red for expired
        label = 'Expired';
    } else if (daysUntilExpiry <= 7) {
        color = '#ff9800'; // orange for expiring soon
        label = `${daysUntilExpiry} days left`;
    }
    
    return (
        <Chip
            label={label}
            size="small"
            sx={{
                backgroundColor: color,
                color: 'white',
                fontWeight: 500,
            }}
        />
    );
};

export const QuoteList = () => (
    <List
        filters={quoteFilters}
        actions={<QuoteListActions />}
        sort={{ field: 'created_at', order: 'DESC' }}
        perPage={25}
    >
        <Datagrid rowClick="show" bulkActionButtons={false}>
            <TextField source="quote_number" />
            <TextField source="title" />
            <ReferenceField source="company_id" reference="companies" link="show">
                <TextField source="name" />
            </ReferenceField>
            <ReferenceField source="contact_id" reference="contacts" link="show">
                <FunctionField
                    render={(record: any) => `${record.first_name} ${record.last_name}`}
                />
            </ReferenceField>
            <FunctionField source="status" render={QuoteStatusField} />
            <NumberField 
                source="total_amount" 
                options={{ style: 'currency', currency: 'PLN' }}
            />
            <DateField source="valid_until" />
            <FunctionField source="valid_until" render={ValidityField} />
            <ReferenceField source="technician_id" reference="technicians" link={false}>
                <FunctionField
                    render={(record: any) => 
                        record ? `${record.first_name} ${record.last_name}` : 'Unassigned'
                    }
                />
            </ReferenceField>
            <DateField source="created_at" showTime />
            <ShowButton />
            <EditButton />
            <DeleteButton />
        </Datagrid>
    </List>
);
