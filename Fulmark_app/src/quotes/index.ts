import QuoteIcon from '@mui/icons-material/Description';

import { QuoteList } from './QuoteList';
import { QuoteCreate } from './QuoteCreate';
import { QuoteEdit } from './QuoteEdit';
import { QuoteShow } from './QuoteShow';

export default {
    list: Quote<PERSON>ist,
    create: QuoteCreate,
    edit: QuoteEdit,
    show: QuoteShow,
    icon: QuoteIcon,
    recordRepresentation: (record: any) => `${record.quote_number} - ${record.title}`,
};
