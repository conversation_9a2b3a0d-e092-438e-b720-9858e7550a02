import {
    Edit,
    SimpleForm,
    TextInput,
    NumberInput,
    DateInput,
    SelectInput,
    required,
} from 'react-admin';
import { Box, Typography, Grid, Card, CardContent } from '@mui/material';
import { AttachMoney as MoneyIcon } from '@mui/icons-material';

const transactionTypeChoices = [
    { id: 'income', name: '<PERSON><PERSON><PERSON>ó<PERSON>' },
    { id: 'expense', name: '<PERSON><PERSON><PERSON><PERSON>' },
    { id: 'invoice', name: 'Faktura' },
    { id: 'payment', name: '<PERSON><PERSON><PERSON><PERSON>' },
];

const categoryChoices = [
    { id: 'service_revenue', name: 'Przychody z serwisu' },
    { id: 'installation_revenue', name: 'Przychody z instalacji' },
    { id: 'parts_cost', name: '<PERSON><PERSON><PERSON>' },
    { id: 'labor_cost', name: '<PERSON><PERSON><PERSON> pracy' },
    { id: 'equipment_cost', name: 'Ko<PERSON><PERSON> sprzętu' },
    { id: 'overhead', name: '<PERSON><PERSON><PERSON> ogólne' },
    { id: 'marketing', name: 'Marketing' },
    { id: 'other', name: 'Inne' },
];

const statusChoices = [
    { id: 'pending', name: '<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>' },
    { id: 'completed', name: 'Zakończone' },
    { id: 'cancelled', name: 'Anulowane' },
];

export const FinancesEdit = () => (
    <Edit title="Edytuj Transakcję">
        <SimpleForm>
            <Box sx={{ width: '100%' }}>
                <Card sx={{ mb: 3 }}>
                    <CardContent>
                        <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                            <MoneyIcon sx={{ mr: 1, color: 'primary.main' }} />
                            <Typography variant="h6">Podstawowe Informacje</Typography>
                        </Box>
                        <Grid container spacing={2}>
                            <Grid item xs={12} md={6}>
                                <TextInput
                                    source="title"
                                    label="Tytuł transakcji"
                                    validate={required()}
                                    fullWidth
                                />
                            </Grid>
                            <Grid item xs={12} md={6}>
                                <SelectInput
                                    source="type"
                                    label="Typ transakcji"
                                    choices={transactionTypeChoices}
                                    validate={required()}
                                    fullWidth
                                />
                            </Grid>
                            <Grid item xs={12}>
                                <TextInput
                                    source="description"
                                    label="Opis"
                                    multiline
                                    rows={3}
                                    fullWidth
                                />
                            </Grid>
                        </Grid>
                    </CardContent>
                </Card>

                <Card sx={{ mb: 3 }}>
                    <CardContent>
                        <Typography variant="h6" sx={{ mb: 2 }}>Szczegóły Finansowe</Typography>
                        <Grid container spacing={2}>
                            <Grid item xs={12} md={6}>
                                <NumberInput
                                    source="amount"
                                    label="Kwota (PLN)"
                                    validate={required()}
                                    fullWidth
                                />
                            </Grid>
                            <Grid item xs={12} md={6}>
                                <SelectInput
                                    source="category"
                                    label="Kategoria"
                                    choices={categoryChoices}
                                    validate={required()}
                                    fullWidth
                                />
                            </Grid>
                            <Grid item xs={12} md={6}>
                                <DateInput
                                    source="date"
                                    label="Data transakcji"
                                    validate={required()}
                                    fullWidth
                                />
                            </Grid>
                            <Grid item xs={12} md={6}>
                                <SelectInput
                                    source="status"
                                    label="Status"
                                    choices={statusChoices}
                                    fullWidth
                                />
                            </Grid>
                        </Grid>
                    </CardContent>
                </Card>

                <Card>
                    <CardContent>
                        <Typography variant="h6" sx={{ mb: 2 }}>Dodatkowe Informacje</Typography>
                        <Grid container spacing={2}>
                            <Grid item xs={12} md={6}>
                                <TextInput
                                    source="reference"
                                    label="Numer referencyjny"
                                    fullWidth
                                />
                            </Grid>
                            <Grid item xs={12} md={6}>
                                <TextInput
                                    source="invoice_number"
                                    label="Numer faktury"
                                    fullWidth
                                />
                            </Grid>
                            <Grid item xs={12}>
                                <TextInput
                                    source="notes"
                                    label="Notatki"
                                    multiline
                                    rows={2}
                                    fullWidth
                                />
                            </Grid>
                        </Grid>
                    </CardContent>
                </Card>
            </Box>
        </SimpleForm>
    </Edit>
);
