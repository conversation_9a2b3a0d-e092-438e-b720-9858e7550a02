import {
    Show,
    SimpleShowLayout,
    TextField,
    NumberField,
    DateField,
    EditButton,
    DeleteButton,
    TopToolbar,
} from 'react-admin';
import {
    Box,
    Typography,
    Grid,
    Card,
    CardContent,
    Chip,
} from '@mui/material';
import {
    AttachMoney as MoneyIcon,
    TrendingUp as IncomeIcon,
    TrendingDown as ExpenseIcon,
    Receipt as InvoiceIcon,
    Payment as PaymentIcon,
} from '@mui/icons-material';

const FinancesShowActions = () => (
    <TopToolbar>
        <EditButton />
        <DeleteButton />
    </TopToolbar>
);

const getTypeIcon = (type: string) => {
    switch (type) {
        case 'income':
            return <IncomeIcon />;
        case 'expense':
            return <ExpenseIcon />;
        case 'invoice':
            return <InvoiceIcon />;
        case 'payment':
            return <PaymentIcon />;
        default:
            return <MoneyIcon />;
    }
};

const getTypeColor = (type: string) => {
    switch (type) {
        case 'income':
            return 'success';
        case 'expense':
            return 'error';
        case 'invoice':
            return 'info';
        case 'payment':
            return 'primary';
        default:
            return 'default';
    }
};

const getStatusColor = (status: string) => {
    switch (status) {
        case 'pending':
            return 'warning';
        case 'completed':
            return 'success';
        case 'cancelled':
            return 'error';
        default:
            return 'default';
    }
};

export const FinancesShow = () => (
    <Show actions={<FinancesShowActions />} title="Szczegóły Transakcji">
        <SimpleShowLayout>
            <Box sx={{ width: '100%' }}>
                <Card sx={{ mb: 3 }}>
                    <CardContent>
                        <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                            <MoneyIcon sx={{ mr: 1, color: 'primary.main' }} />
                            <Typography variant="h6">Podstawowe Informacje</Typography>
                        </Box>
                        <Grid container spacing={2}>
                            <Grid item xs={12}>
                                <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, mb: 2 }}>
                                    <TextField source="title" label="Tytuł" variant="h5" />
                                    <TextField 
                                        source="type" 
                                        render={(record) => (
                                            <Chip 
                                                icon={getTypeIcon(record.type)}
                                                label={record.type}
                                                color={getTypeColor(record.type)}
                                                variant="outlined"
                                            />
                                        )}
                                    />
                                </Box>
                            </Grid>
                            <Grid item xs={12}>
                                <TextField source="description" label="Opis" />
                            </Grid>
                        </Grid>
                    </CardContent>
                </Card>

                <Card sx={{ mb: 3 }}>
                    <CardContent>
                        <Typography variant="h6" sx={{ mb: 2 }}>Szczegóły Finansowe</Typography>
                        <Grid container spacing={2}>
                            <Grid item xs={12} md={6}>
                                <NumberField 
                                    source="amount" 
                                    label="Kwota"
                                    options={{
                                        style: 'currency',
                                        currency: 'PLN',
                                    }}
                                />
                            </Grid>
                            <Grid item xs={12} md={6}>
                                <TextField source="category" label="Kategoria" />
                            </Grid>
                            <Grid item xs={12} md={6}>
                                <DateField source="date" label="Data transakcji" />
                            </Grid>
                            <Grid item xs={12} md={6}>
                                <TextField 
                                    source="status" 
                                    label="Status"
                                    render={(record) => (
                                        <Chip 
                                            label={record.status}
                                            color={getStatusColor(record.status)}
                                            size="small"
                                        />
                                    )}
                                />
                            </Grid>
                        </Grid>
                    </CardContent>
                </Card>

                <Card>
                    <CardContent>
                        <Typography variant="h6" sx={{ mb: 2 }}>Dodatkowe Informacje</Typography>
                        <Grid container spacing={2}>
                            <Grid item xs={12} md={6}>
                                <TextField source="reference" label="Numer referencyjny" />
                            </Grid>
                            <Grid item xs={12} md={6}>
                                <TextField source="invoice_number" label="Numer faktury" />
                            </Grid>
                            <Grid item xs={12}>
                                <TextField source="notes" label="Notatki" />
                            </Grid>
                        </Grid>
                    </CardContent>
                </Card>
            </Box>
        </SimpleShowLayout>
    </Show>
);
