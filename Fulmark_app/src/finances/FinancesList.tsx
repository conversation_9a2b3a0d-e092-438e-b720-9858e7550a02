import { useState } from 'react';
import {
    List,
    Datagrid,
    TextField,
    NumberField,
    DateField,
    SelectField,
    EditButton,
    ShowButton,
    DeleteButton,
    CreateButton,
    ExportButton,
    FilterButton,
    TopToolbar,
    useListContext,
} from 'react-admin';
import {
    Card,
    CardContent,
    Typography,
    Box,
    Grid,
    Button,
    Chip,
} from '@mui/material';
import {
    AttachMoney as MoneyIcon,
    TrendingUp as TrendingUpIcon,
    TrendingDown as TrendingDownIcon,
    AccountBalance as BankIcon,
    Receipt as InvoiceIcon,
    Payment as PaymentIcon,
    Analytics as AnalyticsIcon,
} from '@mui/icons-material';
import { PieChart, Pie, Cell, BarChart, Bar, XAxis, YAxis, CartesianGrid, Tooltip, Legend, ResponsiveContainer } from 'recharts';

const FinancesActions = () => (
    <TopToolbar>
        <FilterButton />
        <CreateButton />
        <ExportButton />
    </TopToolbar>
);

const transactionTypeChoices = [
    { id: 'income', name: '<PERSON><PERSON><PERSON><PERSON><PERSON>' },
    { id: 'expense', name: '<PERSON><PERSON><PERSON><PERSON>' },
    { id: 'invoice', name: '<PERSON><PERSON><PERSON>' },
    { id: 'payment', name: 'Płatność' },
];

const categoryChoices = [
    { id: 'service_revenue', name: 'Przychody z serwisu' },
    { id: 'installation_revenue', name: 'Przychody z instalacji' },
    { id: 'parts_cost', name: 'Koszty części' },
    { id: 'labor_cost', name: 'Koszty pracy' },
    { id: 'equipment_cost', name: 'Koszty sprzętu' },
    { id: 'overhead', name: 'Koszty ogólne' },
    { id: 'marketing', name: 'Marketing' },
    { id: 'other', name: 'Inne' },
];

const statusChoices = [
    { id: 'pending', name: 'Oczekujące' },
    { id: 'completed', name: 'Zakończone' },
    { id: 'cancelled', name: 'Anulowane' },
];

const FinancialDashboard = () => {
    const { data, isLoading } = useListContext();

    if (isLoading) return <div>Ładowanie danych finansowych...</div>;

    // Calculate financial metrics
    const totalIncome = data?.filter(t => t.type === 'income').reduce((sum, t) => sum + (t.amount || 0), 0) || 0;
    const totalExpenses = data?.filter(t => t.type === 'expense').reduce((sum, t) => sum + (t.amount || 0), 0) || 0;
    const netProfit = totalIncome - totalExpenses;
    const profitMargin = totalIncome > 0 ? ((netProfit / totalIncome) * 100).toFixed(1) : '0';

    // Prepare chart data
    const categoryData = categoryChoices.map(category => {
        const categoryTransactions = data?.filter(t => t.category === category.id) || [];
        const total = categoryTransactions.reduce((sum, t) => sum + (t.amount || 0), 0);
        return {
            name: category.name,
            value: total,
            count: categoryTransactions.length,
        };
    }).filter(item => item.value > 0);

    const monthlyData = [
        { month: 'Sty', income: 45000, expenses: 32000 },
        { month: 'Lut', income: 52000, expenses: 35000 },
        { month: 'Mar', income: 48000, expenses: 33000 },
        { month: 'Kwi', income: 61000, expenses: 38000 },
        { month: 'Maj', income: 55000, expenses: 36000 },
        { month: 'Cze', income: 67000, expenses: 41000 },
    ];

    const COLORS = ['#0088FE', '#00C49F', '#FFBB28', '#FF8042', '#8884D8', '#82CA9D'];

    return (
        <Box sx={{ p: 2 }}>
            <Grid container spacing={3}>
                {/* Financial KPIs */}
                <Grid item xs={12} md={3}>
                    <Card>
                        <CardContent>
                            <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                                <Box>
                                    <Typography color="textSecondary" gutterBottom>
                                        Przychody
                                    </Typography>
                                    <Typography variant="h5">
                                        {(totalIncome / 1000).toFixed(1)}k PLN
                                    </Typography>
                                    <Chip 
                                        icon={<TrendingUpIcon />} 
                                        label="+12.5%" 
                                        color="success" 
                                        size="small" 
                                    />
                                </Box>
                                <MoneyIcon sx={{ fontSize: 40, color: 'success.main' }} />
                            </Box>
                        </CardContent>
                    </Card>
                </Grid>

                <Grid item xs={12} md={3}>
                    <Card>
                        <CardContent>
                            <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                                <Box>
                                    <Typography color="textSecondary" gutterBottom>
                                        Wydatki
                                    </Typography>
                                    <Typography variant="h5">
                                        {(totalExpenses / 1000).toFixed(1)}k PLN
                                    </Typography>
                                    <Chip 
                                        icon={<TrendingDownIcon />} 
                                        label="+8.2%" 
                                        color="warning" 
                                        size="small" 
                                    />
                                </Box>
                                <TrendingDownIcon sx={{ fontSize: 40, color: 'error.main' }} />
                            </Box>
                        </CardContent>
                    </Card>
                </Grid>

                <Grid item xs={12} md={3}>
                    <Card>
                        <CardContent>
                            <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                                <Box>
                                    <Typography color="textSecondary" gutterBottom>
                                        Zysk Netto
                                    </Typography>
                                    <Typography variant="h5" color={netProfit >= 0 ? 'success.main' : 'error.main'}>
                                        {(netProfit / 1000).toFixed(1)}k PLN
                                    </Typography>
                                    <Chip 
                                        label={`Marża: ${profitMargin}%`}
                                        color={parseFloat(profitMargin) >= 20 ? 'success' : 'warning'}
                                        size="small" 
                                    />
                                </Box>
                                <AnalyticsIcon sx={{ fontSize: 40, color: 'primary.main' }} />
                            </Box>
                        </CardContent>
                    </Card>
                </Grid>

                <Grid item xs={12} md={3}>
                    <Card>
                        <CardContent>
                            <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                                <Box>
                                    <Typography color="textSecondary" gutterBottom>
                                        Transakcje
                                    </Typography>
                                    <Typography variant="h5">
                                        {data?.length || 0}
                                    </Typography>
                                    <Typography variant="body2" color="textSecondary">
                                        W tym miesiącu
                                    </Typography>
                                </Box>
                                <PaymentIcon sx={{ fontSize: 40, color: 'info.main' }} />
                            </Box>
                        </CardContent>
                    </Card>
                </Grid>

                {/* Charts */}
                <Grid item xs={12} md={8}>
                    <Card>
                        <CardContent>
                            <Typography variant="h6" gutterBottom>
                                Przychody vs Wydatki (miesięcznie)
                            </Typography>
                            <ResponsiveContainer width="100%" height={300}>
                                <BarChart data={monthlyData}>
                                    <CartesianGrid strokeDasharray="3 3" />
                                    <XAxis dataKey="month" />
                                    <YAxis />
                                    <Tooltip formatter={(value) => [`${value} PLN`, '']} />
                                    <Legend />
                                    <Bar dataKey="income" fill="#4caf50" name="Przychody" />
                                    <Bar dataKey="expenses" fill="#f44336" name="Wydatki" />
                                </BarChart>
                            </ResponsiveContainer>
                        </CardContent>
                    </Card>
                </Grid>

                <Grid item xs={12} md={4}>
                    <Card>
                        <CardContent>
                            <Typography variant="h6" gutterBottom>
                                Kategorie Wydatków
                            </Typography>
                            <ResponsiveContainer width="100%" height={300}>
                                <PieChart>
                                    <Pie
                                        data={categoryData}
                                        cx="50%"
                                        cy="50%"
                                        labelLine={false}
                                        label={({ name, percent }) => `${name} ${(percent * 100).toFixed(0)}%`}
                                        outerRadius={80}
                                        fill="#8884d8"
                                        dataKey="value"
                                    >
                                        {categoryData.map((entry, index) => (
                                            <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                                        ))}
                                    </Pie>
                                    <Tooltip formatter={(value) => [`${value} PLN`, 'Wartość']} />
                                </PieChart>
                            </ResponsiveContainer>
                        </CardContent>
                    </Card>
                </Grid>
            </Grid>
        </Box>
    );
};

export const FinancesList = () => {
    const [viewMode, setViewMode] = useState<'dashboard' | 'list'>('dashboard');

    return (
        <List actions={<FinancesActions />} title="Finanse HVAC">
            <Box sx={{ mb: 2 }}>
                <Button
                    variant={viewMode === 'dashboard' ? 'contained' : 'outlined'}
                    onClick={() => setViewMode('dashboard')}
                    sx={{ mr: 1 }}
                >
                    <AnalyticsIcon sx={{ mr: 1 }} />
                    Dashboard
                </Button>
                <Button
                    variant={viewMode === 'list' ? 'contained' : 'outlined'}
                    onClick={() => setViewMode('list')}
                >
                    Lista Transakcji
                </Button>
            </Box>

            {viewMode === 'dashboard' ? (
                <FinancialDashboard />
            ) : (
                <Datagrid>
                    <TextField source="title" label="Tytuł" />
                    <SelectField source="type" choices={transactionTypeChoices} label="Typ" />
                    <SelectField source="category" choices={categoryChoices} label="Kategoria" />
                    <NumberField source="amount" label="Kwota (PLN)" />
                    <DateField source="date" label="Data" />
                    <SelectField source="status" choices={statusChoices} label="Status" />
                    <TextField source="reference" label="Referencja" />
                    <ShowButton />
                    <EditButton />
                    <DeleteButton />
                </Datagrid>
            )}
        </List>
    );
};
