import {
    Show,
    SimpleShowLayout,
    TextField,
    DateField,
    ReferenceField,
    EditButton,
    DeleteButton,
    TopToolbar,
} from 'react-admin';
import {
    Box,
    Typography,
    Grid,
    Card,
    CardContent,
    Chip,
    Divider,
} from '@mui/material';
import {
    CalendarToday as CalendarIcon,
    Person as PersonIcon,
    LocationOn as LocationIcon,
    Build as ServiceIcon,
    Schedule as ScheduleIcon,
    Engineering as TechnicianIcon,
} from '@mui/icons-material';

const CalendarShowActions = () => (
    <TopToolbar>
        <EditButton />
        <DeleteButton />
    </TopToolbar>
);

const getServiceTypeIcon = (type: string) => {
    switch (type) {
        case 'serwis':
            return '🔧';
        case 'instalacja':
            return '🏗️';
        case 'ogledziny':
            return '🔍';
        default:
            return '📅';
    }
};

const getStatusColor = (status: string) => {
    switch (status) {
        case 'scheduled':
            return 'info';
        case 'confirmed':
            return 'primary';
        case 'in_progress':
            return 'warning';
        case 'completed':
            return 'success';
        case 'cancelled':
            return 'error';
        default:
            return 'default';
    }
};

const getPriorityColor = (priority: string) => {
    switch (priority) {
        case 'low':
            return 'success';
        case 'medium':
            return 'info';
        case 'high':
            return 'warning';
        case 'urgent':
            return 'error';
        default:
            return 'default';
    }
};

export const CalendarShow = () => (
    <Show actions={<CalendarShowActions />} title="Szczegóły Wydarzenia">
        <SimpleShowLayout>
            <Box sx={{ width: '100%' }}>
                <Card sx={{ mb: 3 }}>
                    <CardContent>
                        <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                            <CalendarIcon sx={{ mr: 1, color: 'primary.main' }} />
                            <Typography variant="h6">Podstawowe Informacje</Typography>
                        </Box>
                        <Grid container spacing={2}>
                            <Grid item xs={12}>
                                <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, mb: 2 }}>
                                    <TextField source="title" label="Tytuł" variant="h5" />
                                    <TextField 
                                        source="service_type" 
                                        render={(record) => (
                                            <Chip 
                                                label={`${getServiceTypeIcon(record.service_type)} ${record.service_type}`}
                                                color="primary"
                                                variant="outlined"
                                            />
                                        )}
                                    />
                                </Box>
                            </Grid>
                            <Grid item xs={12}>
                                <TextField source="description" label="Opis" />
                            </Grid>
                        </Grid>
                    </CardContent>
                </Card>

                <Card sx={{ mb: 3 }}>
                    <CardContent>
                        <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                            <PersonIcon sx={{ mr: 1, color: 'primary.main' }} />
                            <Typography variant="h6">Klient</Typography>
                        </Box>
                        <Grid container spacing={2}>
                            <Grid item xs={12} md={6}>
                                <ReferenceField source="customer_id" reference="klienci" label="Klient">
                                    <TextField source="full_name" />
                                </ReferenceField>
                            </Grid>
                            <Grid item xs={12} md={6}>
                                <TextField source="customer_name" label="Nazwa klienta (backup)" />
                            </Grid>
                        </Grid>
                    </CardContent>
                </Card>

                <Card sx={{ mb: 3 }}>
                    <CardContent>
                        <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                            <LocationIcon sx={{ mr: 1, color: 'primary.main' }} />
                            <Typography variant="h6">Lokalizacja</Typography>
                        </Box>
                        <Grid container spacing={2}>
                            <Grid item xs={12}>
                                <TextField source="address" label="Adres" />
                            </Grid>
                            <Grid item xs={12} md={6}>
                                <TextField source="city" label="Miasto" />
                            </Grid>
                            <Grid item xs={12} md={6}>
                                <TextField source="postal_code" label="Kod pocztowy" />
                            </Grid>
                        </Grid>
                    </CardContent>
                </Card>

                <Card sx={{ mb: 3 }}>
                    <CardContent>
                        <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                            <ScheduleIcon sx={{ mr: 1, color: 'primary.main' }} />
                            <Typography variant="h6">Planowanie</Typography>
                        </Box>
                        <Grid container spacing={2}>
                            <Grid item xs={12} md={6}>
                                <DateField 
                                    source="start_date" 
                                    label="Data rozpoczęcia"
                                    showTime
                                />
                            </Grid>
                            <Grid item xs={12} md={6}>
                                <DateField 
                                    source="end_date" 
                                    label="Data zakończenia"
                                    showTime
                                />
                            </Grid>
                            <Grid item xs={12} md={4}>
                                <TextField 
                                    source="priority" 
                                    label="Priorytet"
                                    render={(record) => (
                                        <Chip 
                                            label={record.priority}
                                            color={getPriorityColor(record.priority)}
                                            size="small"
                                        />
                                    )}
                                />
                            </Grid>
                            <Grid item xs={12} md={4}>
                                <TextField 
                                    source="status" 
                                    label="Status"
                                    render={(record) => (
                                        <Chip 
                                            label={record.status}
                                            color={getStatusColor(record.status)}
                                            size="small"
                                        />
                                    )}
                                />
                            </Grid>
                            <Grid item xs={12} md={4}>
                                <TextField source="estimated_duration" label="Szacowany czas (godz.)" />
                            </Grid>
                        </Grid>
                    </CardContent>
                </Card>

                <Card>
                    <CardContent>
                        <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                            <TechnicianIcon sx={{ mr: 1, color: 'primary.main' }} />
                            <Typography variant="h6">Technik</Typography>
                        </Box>
                        <Grid container spacing={2}>
                            <Grid item xs={12} md={6}>
                                <ReferenceField source="technician_id" reference="technicy" label="Technik">
                                    <TextField source="full_name" />
                                </ReferenceField>
                            </Grid>
                            <Grid item xs={12} md={6}>
                                <TextField source="technician_name" label="Nazwa technika (backup)" />
                            </Grid>
                            <Grid item xs={12}>
                                <TextField source="notes" label="Notatki dla technika" />
                            </Grid>
                        </Grid>
                    </CardContent>
                </Card>
            </Box>
        </SimpleShowLayout>
    </Show>
);
