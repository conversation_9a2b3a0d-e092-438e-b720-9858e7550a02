import { useState } from 'react';
import {
    List,
    Datagrid,
    TextField,
    DateField,
    SelectField,
    EditButton,
    ShowButton,
    DeleteButton,
    CreateButton,
    ExportButton,
    FilterButton,
    TopToolbar,
    useListContext,
} from 'react-admin';
import {
    Card,
    CardContent,
    Typography,
    Box,
    Chip,
    Grid,
    Button,
    Dialog,
    DialogTitle,
    DialogContent,
} from '@mui/material';
import {
    CalendarToday as CalendarIcon,
    Build as ServiceIcon,
    Construction as InstallIcon,
    Search as InspectionIcon,
    Event as EventIcon,
} from '@mui/icons-material';
import FullCalendar from '@fullcalendar/react';
import dayGridPlugin from '@fullcalendar/daygrid';
import timeGridPlugin from '@fullcalendar/timegrid';
import interactionPlugin from '@fullcalendar/interaction';
import plLocale from '@fullcalendar/core/locales/pl';

const CalendarActions = () => (
    <TopToolbar>
        <FilterButton />
        <CreateButton />
        <ExportButton />
    </TopToolbar>
);

const serviceTypeChoices = [
    { id: 'serwis', name: '🔧 <PERSON><PERSON><PERSON>' },
    { id: 'instalacja', name: '🏗️ Nowa Instalacja' },
    { id: 'ogledziny', name: '🔍 Oględziny' },
];

const priorityChoices = [
    { id: 'low', name: 'Niski' },
    { id: 'medium', name: 'Średni' },
    { id: 'high', name: 'Wysoki' },
    { id: 'urgent', name: 'Pilny' },
];

const statusChoices = [
    { id: 'scheduled', name: 'Zaplanowane' },
    { id: 'in_progress', name: 'W trakcie' },
    { id: 'completed', name: 'Zakończone' },
    { id: 'cancelled', name: 'Anulowane' },
];

const CalendarView = () => {
    const { data, isLoading } = useListContext();
    const [selectedEvent, setSelectedEvent] = useState(null);
    const [dialogOpen, setDialogOpen] = useState(false);

    if (isLoading) return <div>Ładowanie kalendarza...</div>;

    const events = data?.map(event => ({
        id: event.id,
        title: `${event.title} - ${event.customer_name}`,
        start: event.start_date,
        end: event.end_date,
        backgroundColor: getEventColor(event.service_type, event.priority),
        borderColor: getEventColor(event.service_type, event.priority),
        extendedProps: event,
    })) || [];

    const handleEventClick = (clickInfo) => {
        setSelectedEvent(clickInfo.event.extendedProps);
        setDialogOpen(true);
    };

    return (
        <Box sx={{ p: 2 }}>
            <Grid container spacing={3}>
                <Grid item xs={12}>
                    <Card>
                        <CardContent>
                            <Typography variant="h6" gutterBottom>
                                Kalendarz HVAC - Planowanie Serwisu
                            </Typography>
                            <Box sx={{ display: 'flex', gap: 2, mb: 2 }}>
                                <Chip icon={<ServiceIcon />} label="🔧 Serwis" size="small" />
                                <Chip icon={<InstallIcon />} label="🏗️ Instalacja" size="small" />
                                <Chip icon={<InspectionIcon />} label="🔍 Oględziny" size="small" />
                            </Box>
                            <FullCalendar
                                plugins={[dayGridPlugin, timeGridPlugin, interactionPlugin]}
                                headerToolbar={{
                                    left: 'prev,next today',
                                    center: 'title',
                                    right: 'dayGridMonth,timeGridWeek,timeGridDay'
                                }}
                                initialView="dayGridMonth"
                                locale={plLocale}
                                events={events}
                                eventClick={handleEventClick}
                                height="auto"
                                dayMaxEvents={3}
                                moreLinkClick="popover"
                            />
                        </CardContent>
                    </Card>
                </Grid>
            </Grid>

            <Dialog open={dialogOpen} onClose={() => setDialogOpen(false)} maxWidth="md" fullWidth>
                <DialogTitle>
                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                        <EventIcon />
                        Szczegóły Wydarzenia
                    </Box>
                </DialogTitle>
                <DialogContent>
                    {selectedEvent && (
                        <Box sx={{ p: 2 }}>
                            <Typography variant="h6">{selectedEvent.title}</Typography>
                            <Typography variant="body1">Klient: {selectedEvent.customer_name}</Typography>
                            <Typography variant="body2">Typ: {selectedEvent.service_type}</Typography>
                            <Typography variant="body2">Status: {selectedEvent.status}</Typography>
                            <Typography variant="body2">Priorytet: {selectedEvent.priority}</Typography>
                            <Typography variant="body2">Technik: {selectedEvent.technician_name}</Typography>
                            <Typography variant="body2">Adres: {selectedEvent.address}</Typography>
                            {selectedEvent.description && (
                                <Typography variant="body2">Opis: {selectedEvent.description}</Typography>
                            )}
                        </Box>
                    )}
                </DialogContent>
            </Dialog>
        </Box>
    );
};

function getEventColor(serviceType: string, priority: string) {
    const baseColors = {
        serwis: '#2196f3',
        instalacja: '#4caf50', 
        ogledziny: '#ff9800',
    };
    
    const priorityModifier = {
        low: 0.6,
        medium: 0.8,
        high: 1.0,
        urgent: 1.2,
    };
    
    const baseColor = baseColors[serviceType] || '#757575';
    const modifier = priorityModifier[priority] || 1.0;
    
    return baseColor;
}

export const CalendarList = () => {
    const [viewMode, setViewMode] = useState<'calendar' | 'list'>('calendar');

    return (
        <List actions={<CalendarActions />} title="Kalendarz HVAC">
            <Box sx={{ mb: 2 }}>
                <Button
                    variant={viewMode === 'calendar' ? 'contained' : 'outlined'}
                    onClick={() => setViewMode('calendar')}
                    sx={{ mr: 1 }}
                >
                    <CalendarIcon sx={{ mr: 1 }} />
                    Widok Kalendarza
                </Button>
                <Button
                    variant={viewMode === 'list' ? 'contained' : 'outlined'}
                    onClick={() => setViewMode('list')}
                >
                    Lista
                </Button>
            </Box>

            {viewMode === 'calendar' ? (
                <CalendarView />
            ) : (
                <Datagrid>
                    <TextField source="title" label="Tytuł" />
                    <TextField source="customer_name" label="Klient" />
                    <SelectField source="service_type" choices={serviceTypeChoices} label="Typ" />
                    <DateField source="start_date" label="Data rozpoczęcia" />
                    <DateField source="end_date" label="Data zakończenia" />
                    <SelectField source="status" choices={statusChoices} label="Status" />
                    <SelectField source="priority" choices={priorityChoices} label="Priorytet" />
                    <TextField source="technician_name" label="Technik" />
                    <ShowButton />
                    <EditButton />
                    <DeleteButton />
                </Datagrid>
            )}
        </List>
    );
};
