import {
    Create,
    SimpleForm,
    TextInput,
    DateTimeInput,
    SelectInput,
    ReferenceInput,
    AutocompleteInput,
    required,
} from 'react-admin';
import { Box, Typography, Grid, Card, CardContent } from '@mui/material';
import { CalendarToday as CalendarIcon } from '@mui/icons-material';

const serviceTypeChoices = [
    { id: 'serwis', name: '🔧 Serwi<PERSON>' },
    { id: 'instalacja', name: '🏗️ Nowa Instalacja' },
    { id: 'ogledziny', name: '🔍 Oględziny' },
];

const priorityChoices = [
    { id: 'low', name: '<PERSON><PERSON>' },
    { id: 'medium', name: '<PERSON><PERSON><PERSON>' },
    { id: 'high', name: '<PERSON><PERSON><PERSON>' },
    { id: 'urgent', name: '<PERSON><PERSON><PERSON>' },
];

const statusChoices = [
    { id: 'scheduled', name: '<PERSON><PERSON><PERSON><PERSON><PERSON>' },
    { id: 'confirmed', name: 'Potwierdzone' },
    { id: 'in_progress', name: '<PERSON> trak<PERSON>' },
    { id: 'completed', name: '<PERSON>ak<PERSON><PERSON>czone' },
    { id: 'cancelled', name: 'Anulowan<PERSON>' },
];

export const CalendarCreate = () => (
    <Create title="Nowe Wydarzenie w Kalendarzu">
        <SimpleForm>
            <Box sx={{ width: '100%' }}>
                <Card sx={{ mb: 3 }}>
                    <CardContent>
                        <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                            <CalendarIcon sx={{ mr: 1, color: 'primary.main' }} />
                            <Typography variant="h6">Podstawowe Informacje</Typography>
                        </Box>
                        <Grid container spacing={2}>
                            <Grid item xs={12} md={6}>
                                <TextInput
                                    source="title"
                                    label="Tytuł wydarzenia"
                                    validate={required()}
                                    fullWidth
                                />
                            </Grid>
                            <Grid item xs={12} md={6}>
                                <SelectInput
                                    source="service_type"
                                    label="Typ usługi"
                                    choices={serviceTypeChoices}
                                    validate={required()}
                                    fullWidth
                                />
                            </Grid>
                            <Grid item xs={12}>
                                <TextInput
                                    source="description"
                                    label="Opis"
                                    multiline
                                    rows={3}
                                    fullWidth
                                />
                            </Grid>
                        </Grid>
                    </CardContent>
                </Card>

                <Card sx={{ mb: 3 }}>
                    <CardContent>
                        <Typography variant="h6" sx={{ mb: 2 }}>Klient i Lokalizacja</Typography>
                        <Grid container spacing={2}>
                            <Grid item xs={12} md={6}>
                                <ReferenceInput source="customer_id" reference="klienci">
                                    <AutocompleteInput
                                        label="Klient"
                                        optionText="full_name"
                                        validate={required()}
                                        fullWidth
                                    />
                                </ReferenceInput>
                            </Grid>
                            <Grid item xs={12} md={6}>
                                <TextInput
                                    source="customer_name"
                                    label="Nazwa klienta (backup)"
                                    fullWidth
                                />
                            </Grid>
                            <Grid item xs={12}>
                                <TextInput
                                    source="address"
                                    label="Adres"
                                    validate={required()}
                                    fullWidth
                                />
                            </Grid>
                            <Grid item xs={12} md={6}>
                                <TextInput
                                    source="city"
                                    label="Miasto"
                                    defaultValue="Warszawa"
                                    fullWidth
                                />
                            </Grid>
                            <Grid item xs={12} md={6}>
                                <TextInput
                                    source="postal_code"
                                    label="Kod pocztowy"
                                    fullWidth
                                />
                            </Grid>
                        </Grid>
                    </CardContent>
                </Card>

                <Card sx={{ mb: 3 }}>
                    <CardContent>
                        <Typography variant="h6" sx={{ mb: 2 }}>Planowanie</Typography>
                        <Grid container spacing={2}>
                            <Grid item xs={12} md={6}>
                                <DateTimeInput
                                    source="start_date"
                                    label="Data i godzina rozpoczęcia"
                                    validate={required()}
                                    fullWidth
                                />
                            </Grid>
                            <Grid item xs={12} md={6}>
                                <DateTimeInput
                                    source="end_date"
                                    label="Data i godzina zakończenia"
                                    validate={required()}
                                    fullWidth
                                />
                            </Grid>
                            <Grid item xs={12} md={4}>
                                <SelectInput
                                    source="priority"
                                    label="Priorytet"
                                    choices={priorityChoices}
                                    defaultValue="medium"
                                    fullWidth
                                />
                            </Grid>
                            <Grid item xs={12} md={4}>
                                <SelectInput
                                    source="status"
                                    label="Status"
                                    choices={statusChoices}
                                    defaultValue="scheduled"
                                    fullWidth
                                />
                            </Grid>
                            <Grid item xs={12} md={4}>
                                <TextInput
                                    source="estimated_duration"
                                    label="Szacowany czas (godz.)"
                                    type="number"
                                    fullWidth
                                />
                            </Grid>
                        </Grid>
                    </CardContent>
                </Card>

                <Card>
                    <CardContent>
                        <Typography variant="h6" sx={{ mb: 2 }}>Przypisanie Technika</Typography>
                        <Grid container spacing={2}>
                            <Grid item xs={12} md={6}>
                                <ReferenceInput source="technician_id" reference="technicy">
                                    <AutocompleteInput
                                        label="Technik"
                                        optionText="full_name"
                                        fullWidth
                                    />
                                </ReferenceInput>
                            </Grid>
                            <Grid item xs={12} md={6}>
                                <TextInput
                                    source="technician_name"
                                    label="Nazwa technika (backup)"
                                    fullWidth
                                />
                            </Grid>
                            <Grid item xs={12}>
                                <TextInput
                                    source="notes"
                                    label="Notatki dla technika"
                                    multiline
                                    rows={2}
                                    fullWidth
                                />
                            </Grid>
                        </Grid>
                    </CardContent>
                </Card>
            </Box>
        </SimpleForm>
    </Create>
);
